{"compilerOptions": {"target": "es2016", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "outDir": "./dist", "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@hooks/*": ["hooks/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@assets/*": ["assets/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "build", "dist"]}