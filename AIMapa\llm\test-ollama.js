/**
 * Test Ollama Provider
 * Verze 0.4.4
 */

require('dotenv').config();
const OllamaProvider = require('./llm-providers/ollama-provider');

async function testOllama() {
  console.log('🧪 Testování Ollama Provider...\n');

  try {
    // Inicializace providera
    const provider = new OllamaProvider({
      model: process.env.OLLAMA_MODEL || 'llama3.2',
      baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
      temperature: 0.7,
      maxTokens: 500
    });

    console.log('✅ Provider inicializován');
    console.log('📋 Informace o modelu:', provider.getModelInfo());
    console.log('🎯 Populární modely:', OllamaProvider.getPopularModels().join(', '));
    console.log();

    // Test připojení
    console.log('🔗 Testování připojení k Ollama serveru...');
    const connectionTest = await provider.testConnection();
    console.log(`${connectionTest ? '✅' : '❌'} Připojení: ${connectionTest ? 'úspěšné' : 'neúspěšné'}`);
    
    if (!connectionTest) {
      console.log('❌ Ollama server není dostupný');
      console.log('💡 Spusťte Ollama server příkazem: ollama serve');
      console.log('💡 Nebo zkontrolujte OLLAMA_BASE_URL');
      return;
    }
    console.log();

    // Test získání seznamu modelů
    console.log('📋 Získávání seznamu dostupných modelů...');
    try {
      const models = await provider.getAvailableModels();
      if (models.length === 0) {
        console.log('⚠️  Žádné modely nejsou stažené');
        console.log('💡 Stáhněte model příkazem: ollama pull llama3.2');
        
        // Pokus o stažení modelu
        console.log(`\n📥 Pokus o stažení modelu ${provider.model}...`);
        const pullResult = await provider.pullModel();
        if (pullResult) {
          console.log('✅ Model byl úspěšně stažen');
        } else {
          console.log('❌ Stažení modelu selhalo');
          return;
        }
      } else {
        console.log(`✅ Nalezeno ${models.length} modelů:`);
        models.forEach(model => {
          const sizeGB = (model.size / (1024 * 1024 * 1024)).toFixed(1);
          console.log(`  - ${model.name} (${sizeGB} GB)`);
        });
      }
    } catch (error) {
      console.log(`❌ Chyba při získávání modelů: ${error.message}`);
    }
    console.log();

    // Test základního dotazu
    console.log('💬 Testování základního dotazu...');
    const prompt = process.argv[2] || 'Jak se dostanu z Prahy do Brna?';
    console.log(`📝 Prompt: "${prompt}"`);
    console.log('⏳ Čekám na odpověď (lokální modely mohou být pomalejší)...');
    
    const startTime = Date.now();
    try {
      const response = await provider.getCompletion(prompt);
      const endTime = Date.now();

      console.log(`⏱️  Doba odezvy: ${endTime - startTime}ms`);
      console.log(`📊 Použité tokeny: ${response.usage.totalTokens} (prompt: ${response.usage.promptTokens}, odpověď: ${response.usage.completionTokens})`);
      console.log(`🤖 Model: ${response.model}`);
      console.log(`🏢 Provider: ${response.provider}`);
      console.log();
      console.log('💭 Odpověď:');
      console.log('─'.repeat(50));
      console.log(response.text);
      console.log('─'.repeat(50));
      console.log();

      console.log('✅ Test Ollama Provider dokončen úspěšně!');
      console.log('💡 Ollama běží lokálně - žádné náklady na API, ale vyžaduje výpočetní výkon!');

    } catch (error) {
      if (error.message.includes('model') && error.message.includes('not found')) {
        console.log(`❌ Model ${provider.model} není dostupný`);
        console.log(`💡 Stáhněte model příkazem: ollama pull ${provider.model}`);
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error('❌ Chyba při testování Ollama Provider:', error.message);
    console.error('\n💡 Zkontrolujte:');
    console.error('   - Ollama server běží (ollama serve)');
    console.error('   - OLLAMA_BASE_URL je správně nastavena');
    console.error('   - Model je stažený (ollama pull <model>)');
    console.error('   - Máte dostatečnou RAM pro model');
    process.exit(1);
  }
}

// Spuštění testu
if (require.main === module) {
  testOllama();
}

module.exports = testOllama;
