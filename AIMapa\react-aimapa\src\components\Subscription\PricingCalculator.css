/* <PERSON><PERSON>lad<PERSON><PERSON> proměnné pro barvy */
:root {
  --primary-green: #2ecc71;
  --primary-green-dark: #27ae60;
  --primary-green-light: #a9dfbf;

  --primary-red: #e74c3c;
  --primary-red-dark: #c0392b;
  --primary-red-light: #f5b7b1;

  --primary-orange: #f39c12;
  --primary-orange-dark: #d35400;
  --primary-orange-light: #fad7a0;

  --dark-bg: #1e272e;
  --light-bg: #f5f6fa;
  --card-bg: #2c3e50;
  --card-hover: #34495e;

  --text-light: #ecf0f1;
  --text-dark: #2c3e50;
  --text-muted: #95a5a6;

  --border-color: #7f8c8d;
  --shadow-color: rgba(0, 0, 0, 0.2);
}

/* <PERSON><PERSON><PERSON><PERSON> */
.pricing-calculator {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  color: var(--text-light);
}

.calculator-title {
  font-size: 1.8rem;
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--primary-green);
  text-align: center;
}

.calculator-subtitle {
  text-align: center;
  color: var(--text-muted);
  margin-bottom: 25px;
}

/* Formulář kalkulačky */
.calculator-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  font-weight: bold;
  color: var(--text-light);
}

.form-group select,
.form-group input,
.form-group textarea {
  padding: 12px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  background-color: var(--dark-bg);
  color: var(--text-light);
  font-family: inherit;
  resize: vertical;
}

.form-group select:focus,
.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-green);
}

.token-count {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-top: 5px;
}

/* Výsledky výpočtu */
.calculation-results {
  background-color: rgba(46, 204, 113, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.calculation-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--primary-green);
  font-size: 1.3rem;
  text-align: center;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.result-item {
  background-color: var(--card-bg);
  padding: 15px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.result-item.highlight {
  background-color: var(--primary-green-dark);
  border: 1px solid var(--primary-green);
}

.result-label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.result-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-light);
}

.result-item.highlight .result-label {
  color: var(--text-light);
}

.result-item.highlight .result-value {
  color: white;
  font-size: 1.4rem;
}

/* Doporučení pro ceny */
.pricing-recommendations {
  background-color: var(--card-bg);
  padding: 15px;
  border-radius: 5px;
}

.pricing-recommendations h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--primary-orange);
  font-size: 1.1rem;
}

.pricing-recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.pricing-recommendations li {
  margin-bottom: 8px;
  color: var(--text-light);
}

.pricing-recommendations li:last-child {
  margin-bottom: 0;
}

/* Responzivní design */
@media (max-width: 768px) {
  .pricing-calculator {
    padding: 20px 15px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .result-grid {
    grid-template-columns: 1fr;
  }
}
