/**
 * Styly pro uživatelský profil v AIMapa
 * Verze 0.3.8.5
 */

/* Tlačítko pro zobrazení profilu */
.user-profile-button {
    position: fixed;
    top: 20px;
    right: 170px; /* Umístění vedle ostatních tlač<PERSON>k */
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #555;
    color: white;
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
    transition: transform 0.2s, background-color 0.2s;
    font-size: 18px;
}

.user-profile-button:hover {
    transform: scale(1.1);
    background-color: #777;
}

.user-profile-button.logged-in {
    background-color: #4CAF50;
    /* Přidání animace pulzování pro zvýraznění přihl<PERSON><PERSON><PERSON> stavu */
    animation: pulse-green 2s infinite;
    border: 2px solid #fff;
}

.user-profile-button.logged-in:hover {
    background-color: #45a049;
    animation: none;
}

/* Animace pulzování pro zvýraznění přihlášeného stavu */
@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* Modální okno profilu */
.user-profile-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.user-profile-content {
    background-color: var(--background-color, #fff);
    color: var(--text-color, #333);
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Záhlaví profilu */
.user-profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color, #ddd);
}

.user-profile-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color, #333);
}

/* Tělo profilu */
.user-profile-body {
    padding: 20px;
}

/* Informace o uživateli */
.user-profile-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.user-profile-avatar {
    margin-right: 20px;
}

.user-profile-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--border-color, #ddd);
}

.user-profile-details h3 {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.user-profile-details p {
    margin: 0;
    color: var(--secondary-text-color, #666);
}

/* Informace o autentizaci */
.user-profile-auth-info {
    background-color: var(--secondary-background-color, #f5f5f5);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #ff4f1f; /* Auth0 oranžová barva */
}

.user-profile-auth-info h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: #ff4f1f;
}

.auth-provider, .auth-status, .auth-last-login {
    display: flex;
    margin-bottom: 8px;
}

.auth-provider-label, .auth-status-label, .auth-last-login-label {
    font-weight: bold;
    margin-right: 10px;
    min-width: 120px;
}

.auth-provider-value {
    font-weight: bold;
    color: #ff4f1f;
}

.user-profile-id {
    font-size: 0.8rem;
    color: #777;
    margin-top: 5px;
}

/* Metadata uživatele */
.user-profile-metadata {
    background-color: var(--secondary-background-color, #f5f5f5);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-metadata h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.user-profile-metadata h5 {
    margin: 15px 0 10px 0;
    font-size: 0.9rem;
    color: #0066cc;
    border-bottom: 1px solid #0066cc;
    padding-bottom: 5px;
}

.metadata-list {
    max-height: 200px;
    overflow-y: auto;
    padding-right: 10px;
    margin-bottom: 15px;
}

.metadata-item {
    display: flex;
    margin-bottom: 5px;
    border-bottom: 1px dotted #ddd;
    padding-bottom: 5px;
}

.metadata-key {
    font-weight: bold;
    margin-right: 10px;
    min-width: 120px;
}

/* Styly pro Supabase data */
.user-profile-metadata h5:nth-of-type(1) {
    color: #3ECF8E; /* Supabase zelená */
    border-bottom-color: #3ECF8E;
}

/* Styly pro Auth0 metadata */
.user-profile-metadata h5:nth-of-type(3) {
    color: #ff4f1f; /* Auth0 oranžová */
    border-bottom-color: #ff4f1f;
}

/* Styly pro zobrazení stavu autentizace */
.auth-provider-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    border-left: 4px solid #ff4f1f; /* Auth0 oranžová barva */
}

.auth-status-active {
    color: #4CAF50;
    font-weight: bold;
    position: relative;
    padding-left: 20px;
}

.auth-status-active::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #4CAF50;
    animation: blink 2s infinite;
}

@keyframes blink {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
}

/* Předplatné uživatele */
.user-profile-subscription {
    background-color: var(--secondary-background-color, #f5f5f5);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-subscription h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.subscription-info {
    margin-bottom: 15px;
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color, #ddd);
}

.subscription-plan-name {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--primary-color, #8B5CF6);
}

.subscription-plan-price {
    font-weight: bold;
}

.subscription-details {
    margin-bottom: 10px;
}

.subscription-details p {
    margin: 5px 0;
}

.subscription-features {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 10px;
    border-radius: 5px;
}

.subscription-features h5 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

.subscription-features ul {
    margin: 0;
    padding-left: 20px;
}

.subscription-features li {
    margin-bottom: 3px;
    font-size: 0.9rem;
}

.subscription-button {
    background-color: var(--primary-color, #8B5CF6);
    margin-top: 10px;
}

/* Akce profilu */
.user-profile-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.profile-action-button {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
    flex: 1;
}

#updateProfileButton {
    background-color: #4CAF50;
    color: white;
}

#updateProfileButton:hover {
    background-color: #45a049;
}

#logoutButton {
    background-color: #f44336;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#logoutButton:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#logoutButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

#logoutButton:hover::before {
    left: 100%;
}

#logoutButton::after {
    content: '🚪';
    font-size: 16px;
    margin-left: 5px;
}

/* Responzivní design */
@media (max-width: 600px) {
    .user-profile-info {
        flex-direction: column;
        text-align: center;
    }

    .user-profile-avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .user-profile-actions {
        flex-direction: column;
    }
}
