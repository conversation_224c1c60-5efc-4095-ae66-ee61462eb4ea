/**
 * <PERSON><PERSON>y pro denní o<PERSON> v AIMapa
 * Verze 0.3.8.6
 */

/* Notifikace o denní od<PERSON>ěně */
.daily-reward-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #2ecc71;
    color: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    padding: 20px;
    z-index: 1000;
    animation: slide-in-right 0.5s ease-out forwards;
    text-align: center;
}

@keyframes slide-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.daily-reward-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.daily-reward-icon {
    font-size: 40px;
    margin: 10px 0;
    animation: bounce 1s infinite alternate;
}

@keyframes bounce {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-10px);
    }
}

.daily-reward-message {
    margin-bottom: 15px;
    font-size: 16px;
}

.daily-reward-button {
    background-color: white;
    color: #2ecc71;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.daily-reward-button:hover {
    background-color: #f5f5f5;
    transform: scale(1.05);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Animace pro tlačítko */
.daily-reward-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.daily-reward-button:hover::before {
    left: 100%;
}

/* Responzivní design */
@media (max-width: 768px) {
    .daily-reward-notification {
        width: 80%;
        bottom: 10px;
        right: 10px;
    }
}
