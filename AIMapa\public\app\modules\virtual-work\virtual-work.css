/**
 * <PERSON><PERSON><PERSON><PERSON>ý styl pro virtuální pr<PERSON>ci
 * Verze 0.3.5.0
 */

/* Hlavní dialog */
.virtual-work-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    width: 95%;
    max-width: 900px;
    overflow: hidden;
    resize: both;
    min-width: 700px;
    min-height: 500px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.virtual-work-dialog.dragging {
    transition: none;
    cursor: move;
    user-select: none;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 2px 10px rgba(0, 0, 0, 0.2);
}

.virtual-work-dialog:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 1px 5px rgba(0, 0, 0, 0.15);
}

/* Hlavička dialogu */
.virtual-work-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 12px 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
}

.virtual-work-header::before {
    content: '⋮⋮⋮';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0.6;
    letter-spacing: 1px;
}

.virtual-work-header:hover::before {
    opacity: 1;
}

.virtual-work-header h2 {
    margin: 0 auto;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    padding-left: 15px; /* Kompenzace pro ikonu přesouvání */
}

.virtual-work-header h2::before {
    content: '💼';
    margin-right: 8px;
    font-size: 20px;
}

.virtual-work-close {
    background: none;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.virtual-work-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Obsah dialogu */
.virtual-work-content {
    padding: 20px;
    overflow-y: auto;
    max-height: 550px;
    background-color: #f9f9f9;
    width: 100%;
    box-sizing: border-box;
}

/* Kategorie pracovišť */
.workplace-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    background-color: white;
    color: #555;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.category-btn:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
}

.category-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

/* Pracoviště */
.workplace-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    max-height: 450px;
    overflow-y: auto;
    padding-right: 15px;
    padding-left: 15px;
    width: 100%;
    min-width: 650px;
}

.workplace-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    margin-bottom: 10px;
    width: 100%;
    min-width: 600px;
    box-sizing: border-box;
    height: 100px;
}

.workplace-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: #e0e0e0;
    transition: all 0.2s ease;
}

.workplace-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
    border-color: #d0d7de;
}

.workplace-item.selected {
    border-color: #3498db;
    background-color: #ebf7ff;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
    transform: translateY(-2px);
}

.workplace-item.selected::after {
    background-color: #3498db;
    width: 8px;
}

.workplace-icon {
    font-size: 28px;
    background-color: #f0f0f0;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid white;
    position: relative;
    overflow: visible;
    margin-left: 10px;
}

.workplace-icon::before {
    content: attr(data-icon);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.workplace-item:hover .workplace-icon {
    transform: scale(1.1);
}

.workplace-info {
    flex: 1;
}

.workplace-name {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 20px;
    color: #333;
    letter-spacing: -0.2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.workplace-pay {
    color: #27ae60;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 15px;
}

.workplace-pay::before {
    content: '💰';
    margin-right: 6px;
    font-size: 15px;
}

.workplace-description {
    margin-top: 6px;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.5;
    border-left: 3px solid #f0f0f0;
    padding-left: 8px;
    font-style: italic;
}

.workplace-details {
    display: flex;
    gap: 12px;
    margin-top: 10px;
    font-size: 12px;
    flex-wrap: wrap;
}

.workplace-difficulty,
.workplace-xp,
.workplace-duration {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 20px;
    background-color: #f5f5f5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.workplace-item:hover .workplace-difficulty,
.workplace-item:hover .workplace-xp,
.workplace-item:hover .workplace-duration {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workplace-xp::before {
    content: '⭐';
    margin-right: 4px;
}

.workplace-duration::before {
    content: '⏱️';
    margin-right: 4px;
}

/* Statistiky práce */
.work-stats {
    margin-top: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.work-stats h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

.work-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.work-stat {
    text-align: center;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.work-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.work-stat-label {
    font-size: 12px;
    color: #777;
}

/* Tlačítka */
.virtual-work-actions {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    background-color: white;
}

.virtual-work-btn {
    padding: 10px 18px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.virtual-work-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.virtual-work-btn.primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.virtual-work-btn.primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f6aa6);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.virtual-work-btn.primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.virtual-work-btn.secondary {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #e0e0e0;
}

.virtual-work-btn.secondary:hover:not(:disabled) {
    background-color: #e0e0e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.virtual-work-btn.secondary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Výsledek práce */
.work-result {
    text-align: center;
    padding: 25px 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
}

.work-result-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

.work-result-amount {
    font-size: 28px;
    font-weight: bold;
    color: #27ae60;
    margin: 15px 0 5px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideUp 0.5s ease-out;
}

.work-result-xp {
    font-size: 22px;
    font-weight: bold;
    color: #f39c12;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideUp 0.5s ease-out 0.2s;
    animation-fill-mode: both;
}

@keyframes slideUp {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.work-result h3 {
    font-size: 22px;
    margin-bottom: 10px;
    color: #333;
}

.work-result p {
    color: #666;
    margin-bottom: 15px;
    font-size: 15px;
}

/* Progress bar */
.work-progress-container {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    margin: 15px 0;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.work-progress-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 10px;
    transition: width 0.1s linear;
}

.work-progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

/* Activity log */
.work-activity-log {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 15px;
    background-color: #f9f9f9;
}

/* Tlačítko pro uložení práce */
.save-work-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px auto 10px;
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 300px;
}

.save-work-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.save-work-btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.save-work-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Notifikace o uložení práce */
.saved-work-notification {
    position: fixed;
    bottom: 5px;
    right: 5px;
    display: flex;
    align-items: center;
    background-color: #27ae60;
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    max-width: 60px;
    animation: slideIn 0.3s ease-out forwards;
    font-size: 7px;
    opacity: 0.9;
    cursor: pointer;
}

.saved-work-notification:hover {
    opacity: 1;
}

.saved-work-notification.closing {
    animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.saved-work-notification-icon {
    font-size: 7px;
    margin-right: 2px;
}

.saved-work-notification-content {
    flex: 1;
}

.saved-work-notification-title {
    font-weight: bold;
    font-size: 7px;
    margin-bottom: 0;
    line-height: 1;
}

.saved-work-notification-text {
    font-size: 6px;
    opacity: 0.9;
    line-height: 1;
}

.saved-work-notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 8px;
    cursor: pointer;
    padding: 0 0 0 1px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.saved-work-notification-close:hover {
    opacity: 1;
}

.work-activity-item {
    padding: 8px 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 14px;
    color: #555;
    transition: all 0.3s ease;
}

.work-activity-item:last-child {
    border-bottom: none;
}

.work-activity-item.new-activity {
    background-color: #ebf7ff;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    0% { opacity: 0; background-color: #d6eeff; }
    100% { opacity: 1; background-color: #ebf7ff; }
}

/* Vlastní úkoly */
.custom-tasks-container {
    padding: 15px;
}

.custom-tasks-header {
    margin-bottom: 20px;
}

.custom-tasks-header h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 18px;
}

.custom-tasks-header p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.custom-tasks-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
}

.no-tasks {
    padding: 15px;
    text-align: center;
    color: #777;
    font-style: italic;
}

.custom-task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
    transition: all 0.2s ease;
}

.custom-task-item:last-child {
    border-bottom: none;
}

.custom-task-item:hover {
    background-color: #f5f5f5;
}

.custom-task-text {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.custom-task-delete {
    background: none;
    border: none;
    color: #e74c3c;
    font-size: 18px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.custom-task-delete:hover {
    background-color: rgba(231, 76, 60, 0.1);
    transform: scale(1.1);
}

.custom-tasks-form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.custom-task-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.custom-task-input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.custom-tasks-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.custom-tasks-actions button {
    flex: 1;
}

/* Checklist úkolů */
.custom-tasks-progress {
    margin: 15px 0;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.custom-tasks-progress h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.custom-tasks-checklist {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.custom-task-check-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: #f9f9f9;
}

.custom-task-check-item.completed {
    background-color: #e8f7f0;
}

.custom-task-check-item.completed .custom-task-check-text {
    text-decoration: line-through;
    color: #27ae60;
}

.custom-task-checkbox {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    cursor: pointer;
}

.custom-task-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.custom-task-checkbox:hover .checkmark {
    border-color: #3498db;
}

.custom-task-checkbox input:checked ~ .checkmark {
    background-color: #27ae60;
    border-color: #27ae60;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-task-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-task-checkbox .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.custom-task-check-text {
    font-size: 14px;
    color: #333;
    transition: all 0.2s ease;
}

/* Notifikace o dokončení úkolů */
.tasks-completed-notification {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 10px;
    background-color: #e8f7f0;
    border-radius: 6px;
    animation: fadeIn 0.5s ease-out;
}

.tasks-completed-icon {
    font-size: 20px;
    margin-right: 10px;
}

.tasks-completed-text {
    font-weight: bold;
    color: #27ae60;
}

/* Souhrn úkolů */
.tasks-summary {
    margin: 15px 0;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
    text-align: center;
}

.all-tasks-completed {
    color: #27ae60;
    font-weight: bold;
    margin-top: 5px;
    animation: pulse 1.5s infinite alternate;
}

.tasks-bonus {
    color: #f39c12;
    font-weight: bold;
    margin-top: 5px;
}

/* Tlačítko pro manuální dokončení */
.skip-button-container {
    margin-top: 20px;
    text-align: center;
}

#complete-work-manually {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    padding: 12px 20px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    animation: pulse-attention 2s infinite;
}

#complete-work-manually:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

#complete-work-manually:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@keyframes pulse-attention {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Statistiky výsledku */
.work-result-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.work-result-stat {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 8px;
    text-align: center;
}

.work-result-stat-label {
    font-size: 12px;
    color: #777;
    margin-bottom: 5px;
}

.work-result-stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #3498db;
}

/* Tmavý režim */
body[data-theme="dark"] .virtual-work-dialog {
    background-color: #1a2530;
    color: #ecf0f1;
    border-color: #2c3e50;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.2);
}

body[data-theme="dark"] .virtual-work-dialog:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5), 0 1px 5px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .virtual-work-header {
    background: linear-gradient(135deg, #2980b9, #1f6aa6);
    border-bottom-color: #2c3e50;
}

body[data-theme="dark"] .virtual-work-content {
    background-color: #2c3e50;
}

body[data-theme="dark"] .workplace-item {
    border-color: #34495e;
    background-color: #1a2530;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body[data-theme="dark"] .workplace-item::after {
    background-color: #34495e;
}

body[data-theme="dark"] .workplace-item:hover {
    background-color: #243342;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .workplace-item.selected {
    border-color: #3498db;
    background-color: #1f6aa6;
}

body[data-theme="dark"] .workplace-item.selected::after {
    background-color: #3498db;
}

body[data-theme="dark"] .workplace-icon {
    background-color: #34495e;
}

body[data-theme="dark"] .workplace-name {
    color: #ecf0f1;
}

body[data-theme="dark"] .workplace-description {
    color: #bdc3c7;
    border-left-color: #34495e;
}

body[data-theme="dark"] .category-btn {
    background-color: #1a2530;
    border-color: #34495e;
    color: #ecf0f1;
}

body[data-theme="dark"] .category-btn:hover {
    background-color: #243342;
}

body[data-theme="dark"] .category-btn.active {
    background-color: #3498db;
    border-color: #2980b9;
}

body[data-theme="dark"] .workplace-details span {
    background-color: #34495e;
    color: #ecf0f1;
}

body[data-theme="dark"] .work-stats {
    background-color: #1a2530;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body[data-theme="dark"] .work-stats h3 {
    color: #ecf0f1;
}

body[data-theme="dark"] .work-stat {
    background-color: #2c3e50;
}

body[data-theme="dark"] .work-stat-label {
    color: #bdc3c7;
}

body[data-theme="dark"] .virtual-work-actions {
    border-top-color: #34495e;
    background-color: #1a2530;
}

body[data-theme="dark"] .virtual-work-btn.secondary {
    background-color: #34495e;
    color: #ecf0f1;
    border-color: #2c3e50;
}

body[data-theme="dark"] .virtual-work-btn.secondary:hover:not(:disabled) {
    background-color: #2c3e50;
}

body[data-theme="dark"] .work-result {
    background-color: #1a2530;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .work-result h3 {
    color: #ecf0f1;
}

body[data-theme="dark"] .work-result p {
    color: #bdc3c7;
}

body[data-theme="dark"] .work-progress-container {
    background-color: #34495e;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .work-progress-bar {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

body[data-theme="dark"] .work-progress-info {
    color: #bdc3c7;
}

body[data-theme="dark"] .work-activity-log {
    background-color: #2c3e50;
    border-color: #34495e;
}

body[data-theme="dark"] .work-activity-item {
    color: #ecf0f1;
    border-bottom-color: #34495e;
}

body[data-theme="dark"] .work-activity-item.new-activity {
    background-color: #2980b9;
}

body[data-theme="dark"] .save-work-btn {
    background-color: #2980b9;
    color: #ecf0f1;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

body[data-theme="dark"] .save-work-btn:hover {
    background-color: #3498db;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .saved-work-notification {
    background-color: #2c3e50;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

body[data-theme="dark"] .saved-work-notification-title {
    color: #ecf0f1;
}

body[data-theme="dark"] .saved-work-notification-text {
    color: #bdc3c7;
}

@keyframes fadeInDark {
    0% { opacity: 0; background-color: #3498db; }
    100% { opacity: 1; background-color: #2980b9; }
}

body[data-theme="dark"] .work-activity-item.new-activity {
    animation: fadeInDark 0.5s ease-out;
}

body[data-theme="dark"] .work-result-stats {
    border-top-color: #34495e;
}

body[data-theme="dark"] .work-result-stat {
    background-color: #34495e;
}

body[data-theme="dark"] .work-result-stat-label {
    color: #bdc3c7;
}

body[data-theme="dark"] .work-result-stat-value {
    color: #3498db;
}

body[data-theme="dark"] .work-result-xp {
    color: #f39c12;
}

body[data-theme="dark"] #complete-work-manually {
    background: linear-gradient(135deg, #c0392b, #922b21);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

body[data-theme="dark"] #complete-work-manually:hover {
    background: linear-gradient(135deg, #922b21, #7b241c);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

/* Tmavý režim pro vlastní úkoly */
body[data-theme="dark"] .custom-tasks-header h3 {
    color: #ecf0f1;
}

body[data-theme="dark"] .custom-tasks-header p {
    color: #bdc3c7;
}

body[data-theme="dark"] .custom-tasks-list {
    background-color: #2c3e50;
    border-color: #34495e;
}

body[data-theme="dark"] .no-tasks {
    color: #bdc3c7;
}

body[data-theme="dark"] .custom-task-item {
    background-color: #1a2530;
    border-color: #34495e;
}

body[data-theme="dark"] .custom-task-item:hover {
    background-color: #243342;
}

body[data-theme="dark"] .custom-task-text {
    color: #ecf0f1;
}

body[data-theme="dark"] .custom-task-input {
    background-color: #1a2530;
    border-color: #34495e;
    color: #ecf0f1;
}

body[data-theme="dark"] .custom-task-input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

body[data-theme="dark"] .custom-tasks-progress {
    background-color: #1a2530;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body[data-theme="dark"] .custom-tasks-progress h4 {
    color: #ecf0f1;
}

body[data-theme="dark"] .custom-task-check-item {
    background-color: #2c3e50;
}

body[data-theme="dark"] .custom-task-check-item.completed {
    background-color: #27ae60;
}

body[data-theme="dark"] .custom-task-check-item.completed .custom-task-check-text {
    color: #ecf0f1;
}

body[data-theme="dark"] .checkmark {
    background-color: #34495e;
    border-color: #2c3e50;
}

body[data-theme="dark"] .custom-task-check-text {
    color: #ecf0f1;
}

body[data-theme="dark"] .tasks-completed-notification {
    background-color: #27ae60;
}

body[data-theme="dark"] .tasks-completed-text {
    color: #ecf0f1;
}

body[data-theme="dark"] .tasks-summary {
    background-color: #2c3e50;
}

body[data-theme="dark"] .tasks-summary p {
    color: #ecf0f1;
}

/* Styly pro sledování bodů */
.track-points-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.track-points-list {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
}

.track-points-header {
    display: grid;
    grid-template-columns: 40px 1fr 1fr 1fr;
    gap: 10px;
    background-color: #f5f5f5;
    padding: 10px;
    font-weight: bold;
}

.track-point-item {
    display: grid;
    grid-template-columns: 40px 1fr 1fr 1fr;
    gap: 10px;
    padding: 10px;
    border-top: 1px solid #e0e0e0;
}

.track-point-item:hover {
    background-color: #f9f9f9;
}

.track-point-number {
    font-weight: bold;
    text-align: center;
}

.track-points-summary {
    padding: 10px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    font-weight: bold;
}

.no-points {
    padding: 20px;
    text-align: center;
    color: #777;
}

/* Marker na mapě */
.track-point-marker {
    background-color: #3498db;
    border: 2px solid white;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    width: 24px !important;
    height: 24px !important;
    margin-left: -12px !important;
    margin-top: -12px !important;
}

.track-point-number {
    font-size: 12px;
    line-height: 1;
}

.track-point-popup h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.track-point-popup p {
    margin: 0 0 3px 0;
    font-size: 14px;
}

/* Tmavý režim pro sledování bodů */
body[data-theme="dark"] .track-points-list {
    border-color: #34495e;
}

body[data-theme="dark"] .track-points-header {
    background-color: #34495e;
}

body[data-theme="dark"] .track-point-item {
    border-top-color: #34495e;
}

body[data-theme="dark"] .track-point-item:hover {
    background-color: #2c3e50;
}

body[data-theme="dark"] .track-points-summary {
    background-color: #34495e;
    border-top-color: #2c3e50;
}

body[data-theme="dark"] .no-points {
    color: #bdc3c7;
}

/* Styly pro ukládání cest */
.track-path-controls {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.track-path-preview {
    margin-top: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 10px;
    background-color: #f9f9f9;
}

.track-path-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 10px;
}

.track-path-stat {
    background-color: #f0f0f0;
    padding: 8px;
    border-radius: 5px;
    text-align: center;
}

.track-path-stat-label {
    font-size: 12px;
    color: #777;
}

.track-path-stat-value {
    font-size: 16px;
    font-weight: bold;
}

.track-path-name-input {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.saved-paths-list {
    margin-top: 15px;
}

.saved-path-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    margin-bottom: 8px;
    cursor: pointer;
}

.saved-path-item:hover {
    background-color: #f5f5f5;
}

.saved-path-icon {
    font-size: 20px;
    color: #3498db;
}

.saved-path-info {
    flex: 1;
}

.saved-path-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.saved-path-details {
    font-size: 12px;
    color: #777;
}

.saved-path-actions {
    display: flex;
    gap: 5px;
}

.saved-path-action {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    background-color: #f0f0f0;
    color: #555;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.saved-path-action:hover {
    background-color: #e0e0e0;
}

.saved-path-action.delete {
    color: #e74c3c;
}

.saved-path-action.show {
    color: #3498db;
}

/* Styly pro existující cestu */
.existing-paths-section {
    margin-top: 20px;
    border-top: 1px solid #e0e0e0;
    padding-top: 15px;
}

.existing-paths-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

#existing-path-preview {
    margin-bottom: 15px;
}

/* Zvýraznění tlačítka pro import existující cesty */
#import-existing-path {
    background-color: #27ae60;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

#import-existing-path:hover {
    background-color: #2ecc71;
    transform: scale(1.05);
}

/* Tlačítko pro výpočet trasy */
#calculate-route-btn {
    background-color: #3498db;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

#calculate-route-btn:hover {
    background-color: #2980b9;
    transform: scale(1.05);
}

/* Animace pro upozornění na existující cestu */
@keyframes highlight-path {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

.track-path-preview {
    animation: highlight-path 2s ease-in-out;
}

/* Tmavý režim pro ukládání cest */
body[data-theme="dark"] .track-path-preview {
    background-color: #2c3e50;
    border-color: #34495e;
}

body[data-theme="dark"] .track-path-stat {
    background-color: #34495e;
}

body[data-theme="dark"] .track-path-stat-label {
    color: #bdc3c7;
}

body[data-theme="dark"] .track-path-name-input {
    background-color: #34495e;
    border-color: #2c3e50;
    color: #ecf0f1;
}

body[data-theme="dark"] .saved-path-item {
    border-color: #34495e;
}

body[data-theme="dark"] .saved-path-item:hover {
    background-color: #34495e;
}

body[data-theme="dark"] .saved-path-details {
    color: #bdc3c7;
}

body[data-theme="dark"] .saved-path-action {
    background-color: #34495e;
    color: #ecf0f1;
}

body[data-theme="dark"] .existing-paths-section h3 {
    color: #ecf0f1;
}

body[data-theme="dark"] .existing-paths-section {
    border-top-color: #34495e;
}

/* Odměňovací systém */
.reward-selection-container {
    padding: 20px;
    text-align: center;
}

.reward-selection-container h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #3498db;
}

.reward-selection-container p {
    margin-bottom: 20px;
    color: #555;
    font-size: 16px;
}

.reward-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.reward-option {
    width: 200px;
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: left;
}

.reward-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.reward-option.selected {
    background-color: #3498db;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.reward-option.selected::before {
    content: "✓";
    position: absolute;
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    background-color: #27ae60;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.reward-option-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

.reward-option-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.reward-option-value {
    font-size: 22px;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 10px;
}

.reward-option.selected .reward-option-name,
.reward-option.selected .reward-option-value,
.reward-option.selected .reward-option-description {
    color: white;
}

.reward-option-description {
    font-size: 14px;
    color: #777;
    line-height: 1.4;
}

.reward-selection-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
}

.reward-selection-info p {
    margin: 0;
    font-size: 14px;
    color: #555;
}

.work-result-reward {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.work-result-reward h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.work-result-reward-type {
    font-size: 16px;
    color: #3498db;
    font-weight: 500;
}

.reward-bonus {
    display: inline-block;
    margin-left: 5px;
    padding: 2px 6px;
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

/* Tmavý režim pro odměňovací systém */
body[data-theme="dark"] .reward-selection-container h3 {
    color: #3498db;
}

body[data-theme="dark"] .reward-selection-container p {
    color: #ecf0f1;
}

body[data-theme="dark"] .reward-option {
    background-color: #2c3e50;
}

body[data-theme="dark"] .reward-option-name {
    color: #ecf0f1;
}

body[data-theme="dark"] .reward-option-value {
    color: #3498db;
}

body[data-theme="dark"] .reward-option-description {
    color: #bdc3c7;
}

body[data-theme="dark"] .reward-selection-info {
    background-color: #34495e;
}

body[data-theme="dark"] .reward-selection-info p {
    color: #ecf0f1;
}

body[data-theme="dark"] .work-result-reward {
    background-color: #2c3e50;
}

body[data-theme="dark"] .work-result-reward h4 {
    color: #ecf0f1;
}
