# Struktura projektu AIMapa

Tento dokument poskytuje přehled souborů v projektu, jejich ú<PERSON> a stav vývoje.

## Kl<PERSON><PERSON><PERSON><PERSON> soubory

| Soubor | Verze | Stav | Popis |
|--------|-------|------|-------|
| index.html | ******* | Stabilní | Hlavní HTML soubor aplikace |
| script.js | ******* | Stabilní | Hlavní JavaScript soubor s logikou aplikace |
| styles.css | ******* | Stabilní | Hlavní CSS soubor s definicí stylů |

## Moduly a komponenty

| Soubor | Verze | Stav | Popis |
|--------|-------|------|-------|
| updates-notification.js | ******* | Stabilní | Modul pro zobrazení novinek a aktualizací |
| updates-notification.css | ******* | Stabilní | Styly pro modul novinek |
| user-progress.js | ******* | Stabilní | Modul pro sledování postupu uživatele (XP, úrovně) |
| user-progress.css | ******* | Stabilní | Styly pro modul postupu uživatele |
| achievements.js | ******* | Stabilní | Modul pro správu a zobrazení achievementů |
| achievements.css | ******* | Stabilní | Styly pro modul achievementů |
| virtual-work.js | ******* | Stabilní | Modul pro virtuální práci |
| virtual-work.css | ******* | Stabilní | Styly pro modul virtuální práce |
| reward-system.js | ******* | Stabilní | Modul pro odměňovací systém |
| reward-system.css | ******* | Stabilní | Styly pro odměňovací systém |
| task-system.js | ******* | Stabilní | Modul pro systém úkolů a denních questů |
| task-system.css | ******* | Stabilní | Styly pro systém úkolů |
| user-progress-extensions.js | *******.3 | Stabilní | Rozšíření modulu postupu uživatele |
| globe-simple.js | *******.3 | Stabilní | Implementace jednoduchého 3D glóbusu |
| transport-connections.js | *******.3 | Stabilní | Modul pro vyhledávání spojení veřejnou dopravou |
| route-utils.css | *******.3 | Stabilní | Styly pro nástroje tras |
| feedback-survey.js | *******.4 | Nový | Modul pro zpětnou vazbu a dotazník o používání aplikace |
| feedback-survey.css | *******.4 | Nový | Styly pro modul zpětné vazby |

## Dokumentace

| Soubor | Poslední aktualizace | Popis |
|--------|---------------------|-------|
| CHANGELOG.md | *******.4 | Historie změn v projektu |
| PROJECT_STRUCTURE.md | *******.4 | Tento dokument - přehled struktury projektu |

## Vývojový stav modulů

### Stabilní moduly (plně funkční)
- Základní mapové funkce (script.js)
- Notifikace o aktualizacích (updates-notification.js)
- Sledování postupu uživatele (user-progress.js)
- Glóbus režim (globe-simple.js)
- Vyhledávání spojení (transport-connections.js)
- Achievementy (achievements.js)
- Virtuální práce (virtual-work.js)
- Odměňovací systém (reward-system.js)
- Systém úkolů a denních questů (task-system.js)
- Služby bydlení (housing-services.js)
- Služby jídla (food-services.js)
- Lékařské služby (medical-services.js)
- Dopravní služby (transport-services.js)
- Načítání reálných dat podniků (business-data-loader.js)

### Plánované moduly (pro verzi 0.4.0)
- Offline režim
- Synchronizace dat s cloudem
- Uživatelské účty a přihlašování
- Pokročilé statistiky tras
- Rozšířené možnosti sdílení

## Poznámky k vývoji

1. **Priorita oprav pro plný release:**
   - Optimalizace výkonu při načítání aplikace
   - Vylepšení správy paměti a výkonu při dlouhodobém používání
   - Testování kompatibility s různými prohlížeči
   - Oprava zpracování příkazů v menu příkazů

2. **Priorita nových funkcí pro verzi 0.4.0:**
   - Implementace databáze MongoDB pro ukládání dat
   - Přidání uživatelských účtů a přihlašování
   - Implementace cloudové synchronizace dat
   - Rozšíření gamifikačních prvků
   - Implementace offline režimu

## Závislosti

### Externí knihovny
- Leaflet.js - mapový engine
- Leaflet Routing Machine - směrování tras
- OSM Buildings - zobrazení 3D budov
- Three.js - 3D vizualizace
- Globe.gl - vizualizace glóbusu
- Cesium - 3D glóbus (ponecháno pro zpětnou kompatibilitu)

### API
- OpenStreetMap - mapové podklady
- OSRM - směrování tras
- OpenRouteService - alternativní směrování tras
