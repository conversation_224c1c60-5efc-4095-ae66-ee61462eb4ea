/**
 * Styly pro modul uživatelských účtů ve stylu PocketOption.com
 * Verze 0.3.8.5
 */

/* Tlačítko pro zobrazení účtu */
.account-button {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
    transition: transform 0.2s, background-color 0.2s;
}

.account-button:hover {
    transform: scale(1.1);
    background-color: var(--primary-color-dark);
}

.account-button-icon {
    font-size: 1.2rem;
}

/* Okno s účtem */
.account-window {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    width: 400px;
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.4);
    z-index: 1000;
    overflow: hidden;
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
}

.account-window.show {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

.account-window-header {
    background-color: var(--primary-color);
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.account-window-title {
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
}

.account-window-title .icon {
    font-size: 1.3rem;
}

.account-window-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.account-window-content {
    padding: 15px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Profil uživatele */
.account-profile {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.account-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 3px solid var(--primary-color);
    cursor: pointer;
}

.account-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.account-avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
}

.account-avatar:hover .account-avatar-overlay {
    opacity: 1;
}

.account-avatar-change-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.account-avatar-change-text {
    font-size: 12px;
    font-weight: bold;
}

.account-level {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 25px;
    height: 25px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 0.8rem;
    border: 2px solid white;
}

.account-info {
    flex: 1;
}

.account-username {
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: var(--text-color);
}

.account-xp-bar {
    height: 8px;
    background-color: var(--bg-light);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
    position: relative;
}

.account-xp-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0;
    transition: width 0.3s;
}

.account-xp-text {
    font-size: 0.8rem;
    color: var(--text-color-dark);
    text-align: right;
    margin-bottom: 5px;
}

.account-balance {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Záložky */
.account-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.account-tab {
    flex: 1;
    padding: 10px;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color-dark);
    transition: color 0.2s, border-bottom 0.2s;
    border-bottom: 2px solid transparent;
}

.account-tab:hover {
    color: var(--primary-color);
}

.account-tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: bold;
}

.account-tab-content {
    display: none;
}

.account-tab-content.active {
    display: block;
}

/* Statistiky */
.account-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.account-stat-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--bg-light);
    border-radius: 8px;
    transition: transform 0.2s, background-color 0.2s;
}

.account-stat-item:hover {
    transform: translateY(-2px);
    background-color: var(--bg-dark);
}

.account-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    color: white;
    font-size: 1.2rem;
}

.account-stat-info {
    flex: 1;
}

.account-stat-value {
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 3px;
}

.account-stat-label {
    color: var(--text-color-dark);
    font-size: 0.8rem;
}

.account-activity {
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 8px;
}

.account-activity h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-color);
    font-size: 1rem;
}

.account-activity-date {
    color: var(--text-color-dark);
    font-size: 0.9rem;
}

/* Achievementy */
.account-achievements {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.account-achievement {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: var(--bg-light);
    border-radius: 8px;
    transition: transform 0.2s, background-color 0.2s;
}

.account-achievement:hover {
    transform: translateY(-2px);
    background-color: var(--bg-dark);
}

.account-achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f7b731;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    color: white;
    font-size: 1.2rem;
}

.account-achievement-info {
    flex: 1;
}

.account-achievement-name {
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 3px;
}

.account-achievement-description {
    color: var(--text-color-dark);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.account-achievement-date {
    color: var(--text-color-dark);
    font-size: 0.8rem;
    opacity: 0.7;
}

.account-no-achievements {
    padding: 20px;
    text-align: center;
    color: var(--text-color-dark);
    background-color: var(--bg-light);
    border-radius: 8px;
}

/* Nastavení */
.account-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.account-setting-item {
    display: flex;
    flex-direction: column;
}

.account-setting-item label {
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: bold;
}

.account-setting-item input[type="text"],
.account-setting-item input[type="email"],
.account-setting-item select {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-light);
    color: var(--text-color);
}

.account-setting-item input[type="text"]:focus,
.account-setting-item input[type="email"]:focus,
.account-setting-item select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.account-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.account-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.account-toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-dark);
    transition: .4s;
    border-radius: 24px;
}

.account-toggle-label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.account-toggle input:checked + .account-toggle-label {
    background-color: var(--primary-color);
}

.account-toggle input:checked + .account-toggle-label:before {
    transform: translateX(26px);
}

.account-setting-actions {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
}

.account-save-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.account-save-button:hover {
    background-color: var(--primary-color-dark);
}

/* Oznámení */
.account-notification {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 12px 15px;
    display: flex;
    align-items: center;
    z-index: 1100;
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    max-width: 350px;
}

.account-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.account-notification-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.account-notification-message {
    flex: 1;
    color: var(--text-color);
}

.account-notification-close {
    background: none;
    border: none;
    color: var(--text-color-dark);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    margin-left: 10px;
}

.account-notification-info {
    border-left: 4px solid #3498db;
}

.account-notification-success {
    border-left: 4px solid #2ecc71;
}

.account-notification-warning {
    border-left: 4px solid #f39c12;
}

.account-notification-error {
    border-left: 4px solid #e74c3c;
}

.account-notification-achievement {
    border-left: 4px solid #f7b731;
}

/* Tlačítko pro odhlášení */
.logout-button {
    position: fixed;
    top: 20px;
    right: 70px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e74c3c;
    color: white;
    border: none;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
    transition: transform 0.2s, background-color 0.2s;
}

.logout-button:hover {
    transform: scale(1.1);
    background-color: #c0392b;
}

.logout-button-icon {
    font-size: 1.2rem;
}

/* Responzivní design */
@media (max-width: 768px) {
    .account-button {
        top: 15px;
        right: 15px;
        width: 35px;
        height: 35px;
    }

    .logout-button {
        top: 15px;
        right: 60px;
        width: 35px;
        height: 35px;
    }

    .account-window {
        width: 90vw;
        max-width: 400px;
    }

    .account-stats {
        grid-template-columns: 1fr;
    }
}
