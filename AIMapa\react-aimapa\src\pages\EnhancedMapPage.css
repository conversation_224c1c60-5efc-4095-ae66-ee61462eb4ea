/* <PERSON><PERSON>lad<PERSON><PERSON> proměnné pro barvy */
:root {
  --primary-green: #2ecc71;
  --primary-green-dark: #27ae60;
  --primary-green-light: #a9dfbf;

  --primary-red: #e74c3c;
  --primary-red-dark: #c0392b;
  --primary-red-light: #f5b7b1;

  --primary-orange: #f39c12;
  --primary-orange-dark: #d35400;
  --primary-orange-light: #fad7a0;

  --dark-bg: #1e272e;
  --light-bg: #f5f6fa;
  --card-bg: #2c3e50;
  --card-hover: #34495e;

  --text-light: #ecf0f1;
  --text-dark: #2c3e50;
  --text-muted: #95a5a6;

  --border-color: #7f8c8d;
  --shadow-color: rgba(0, 0, 0, 0.2);
}

.enhanced-map-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--dark-bg);
  color: var(--text-light);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.map-provider-selector {
  display: flex;
  align-items: center;
}

.map-provider-selector label {
  margin-right: 10px;
  white-space: nowrap;
  color: var(--text-light);
}

.map-provider-selector select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  background-color: var(--dark-bg);
  color: var(--text-light);
  cursor: pointer;
}

.map-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.api-key-warning {
  display: flex;
  align-items: center;
  background-color: rgba(243, 156, 18, 0.2);
  border-radius: 5px;
  padding: 8px 12px;
  color: var(--primary-orange);
}

.api-key-warning i {
  margin-right: 8px;
  color: var(--primary-orange);
}

.api-manager-button {
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-manager-button:hover {
  background-color: var(--primary-green-dark);
}

.chat-toggle-button {
  background-color: var(--primary-orange);
  color: var(--text-light);
  border: none;
  border-radius: 8px;
  padding: 10px 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chat-toggle-button:hover {
  background-color: var(--primary-orange-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.chat-toggle-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.chat-toggle-button:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

/* Skrýt chat tlačítko - speciální styl */
.chat-toggle-button.hide-chat {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-toggle-button.hide-chat:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
}

.chat-toggle-button i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.chat-toggle-button:hover i {
  transform: scale(1.2);
}

.planning-toggle-button {
  background-color: #3498db;
  color: var(--text-light);
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.planning-toggle-button:hover {
  background-color: #2980b9;
}

.globe-toggle-button {
  background-color: #9b59b6;
  color: var(--text-light);
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.globe-toggle-button:hover {
  background-color: #8e44ad;
}

.test-runner-button {
  background-color: #e74c3c;
  color: var(--text-light);
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-runner-button:hover {
  background-color: #c0392b;
}

.test-runner-button:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
}

.map-content {
  display: flex;
  flex: 1;
  gap: 0;
  height: calc(100vh - 80px); /* Upraveno pro lepší výšku */
  position: relative;
  overflow: hidden;
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  z-index: 1; /* Zajistí, že mapa bude vždy pod panely */
}

.chat-container {
  width: 400px;
  height: 100%;
  border-left: 1px solid var(--border-color);
  background-color: var(--dark-bg);
  display: flex;
  flex-direction: column;
  z-index: 2; /* Zajistí, že chat bude nad mapou */
  position: relative; /* Potřebné pro správné fungování z-indexu */
}

/* Informace o aktivním API klíči */
.active-api-info {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
}

.api-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  color: var(--text-light);
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Modal pro správu API klíčů */
.api-manager-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.api-manager-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.api-manager-modal {
  position: relative;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
  border-radius: 10px;
  animation: modalFadeIn 0.3s ease-in-out;
}

/* Modal pro TestRunner */
.test-runner-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.test-runner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.test-runner-modal {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  z-index: 1001;
  border-radius: 10px;
  animation: modalFadeIn 0.3s ease-in-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.close-modal-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: var(--primary-red);
  color: var(--text-light);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1002;
  transition: background-color 0.3s;
}

.close-modal-button:hover {
  background-color: var(--primary-red-dark);
}

/* Zobrazení informací o souřadnicích a trase */
.coordinates-display {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease-out;
}

.coordinates-display i {
  font-size: 14px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .map-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .map-provider-selector {
    width: 100%;
  }

  .map-provider-selector select {
    flex: 1;
  }

  .map-actions {
    width: 100%;
    flex-wrap: wrap;
  }

  .api-key-warning {
    width: 100%;
    margin-bottom: 10px;
  }

  .api-manager-button,
  .chat-toggle-button {
    flex: 1;
    justify-content: center;
  }

  .map-content {
    flex-direction: column;
    height: auto;
  }

  .map-container {
    height: 50vh;
    min-height: 300px;
  }

  .chat-container {
    width: 100%;
    height: 50vh;
    min-height: 300px;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .api-manager-modal {
    width: 95%;
    max-height: 95vh;
  }

  .test-runner-modal {
    width: 95%;
    max-height: 95vh;
  }
}
