/* Základn<PERSON> styly pro AIMapa verze 0.3.8.2 */

/* <PERSON>set<PERSON><PERSON>í <PERSON><PERSON> sty<PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    width: 100%;
    font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    overflow: hidden;
}

/* Styly pro mapu */
#map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Styly pro překryvnou vrstvu autentizace */
#auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: white;
    text-align: center;
}

#auth-overlay h2 {
    font-size: 24px;
    margin-bottom: 20px;
}

#auth-overlay p {
    font-size: 16px;
    margin-bottom: 30px;
    max-width: 80%;
}

#auth-overlay .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Styly pro tlačítka */
.button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.button:hover {
    background-color: #3367d6;
}

/* Styly pro Auth0 profil */
#auth0-profile {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 5px 10px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#auth0-profile img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

#auth0-profile .user-info {
    display: flex;
    flex-direction: column;
}

#auth0-profile .user-name {
    font-weight: bold;
    font-size: 14px;
}

#auth0-profile .user-email {
    font-size: 12px;
    color: #666;
}

/* Tmavý režim */
.dark-mode {
    background-color: #121212;
    color: #f0f0f0;
}

.dark-mode #auth0-profile {
    background-color: rgba(30, 30, 30, 0.9);
    color: #f0f0f0;
}

.dark-mode #auth0-profile .user-email {
    color: #aaa;
}

/* Responzivní design */
@media (max-width: 768px) {
    #auth0-profile {
        top: 5px;
        right: 5px;
        padding: 3px 8px;
    }
    
    #auth0-profile img {
        width: 24px;
        height: 24px;
    }
    
    #auth0-profile .user-name {
        font-size: 12px;
    }
    
    #auth0-profile .user-email {
        font-size: 10px;
    }
}
