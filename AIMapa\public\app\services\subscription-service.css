/**
 * Styly pro modul předplatného
 * Verze *******
 */

/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> předplatn<PERSON>ho */
.subscription-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background-color: #f5a623;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.subscription-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.subscription-button i {
    font-size: 24px;
}

/* Stavy předplatného */
.subscription-button.plan-free {
    background-color: #9e9e9e;
}

.subscription-button.plan-basic {
    background-color: #4a90e2;
}

.subscription-button.plan-premium {
    background-color: #7b68ee;
}

.subscription-button.plan-ultimate {
    background-color: #f5a623;
    animation: pulse 2s infinite;
}

/* Modální okno předplatného */
.subscription-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.subscription-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 800px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: zoomIn 0.3s ease;
}

.subscription-header {
    padding: 20px;
    background-color: #f5a623;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1;
}

.subscription-header h2 {
    margin: 0;
    font-size: 24px;
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.subscription-body {
    padding: 20px;
}

.subscription-info {
    margin-bottom: 20px;
    text-align: center;
}

.subscription-info p {
    font-size: 16px;
    color: #333;
}

/* Plány předplatného */
.subscription-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.subscription-plan {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.subscription-plan:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.subscription-plan.active {
    border-color: #f5a623;
    box-shadow: 0 0 0 2px rgba(245, 166, 35, 0.3);
}

.plan-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.plan-header h3 {
    margin: 0 0 10px;
    font-size: 20px;
    color: #333;
}

.plan-price {
    font-size: 16px;
    color: #666;
}

.plan-price .price {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.plan-features {
    padding: 20px;
}

.plan-features ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.plan-features li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
    color: #666;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features li::before {
    content: "✓";
    color: #4caf50;
    margin-right: 8px;
    font-weight: bold;
}

.plan-action {
    padding: 0 20px 20px;
    text-align: center;
}

.select-plan-button, .current-plan-button {
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
}

.select-plan-button {
    background-color: #f5a623;
    color: white;
    border: none;
}

.select-plan-button:hover {
    background-color: #e09612;
}

.current-plan-button {
    background-color: #e0e0e0;
    color: #666;
    border: none;
    cursor: default;
}

/* Aktuální předplatné */
.current-subscription-info {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.current-subscription-info h3 {
    margin-top: 0;
    color: #333;
}

.current-subscription-info p {
    margin-bottom: 15px;
    color: #666;
}

.cancel-subscription-button {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.cancel-subscription-button:hover {
    background-color: #e53935;
}

/* Platební sekce */
.subscription-payment {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.subscription-payment h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.card-element {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

.card-errors {
    color: #f44336;
    font-size: 14px;
    margin-top: 8px;
    min-height: 20px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.cancel-button, .payment-button {
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.cancel-button {
    background-color: #e0e0e0;
    color: #666;
    border: none;
}

.cancel-button:hover {
    background-color: #d0d0d0;
}

.payment-button {
    background-color: #4caf50;
    color: white;
    border: none;
}

.payment-button:hover {
    background-color: #43a047;
}

.payment-button:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
}

/* Animace */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 166, 35, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(245, 166, 35, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(245, 166, 35, 0);
    }
}

/* Responzivní design */
@media (max-width: 768px) {
    .subscription-content {
        width: 95%;
    }

    .subscription-plans {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .cancel-button, .payment-button {
        width: 100%;
    }
}
