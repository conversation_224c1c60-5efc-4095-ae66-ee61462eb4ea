/* Styly pro informační panel úkolu */
:root {
  --marker-primary: #3498db;
  --marker-success: #2ecc71;
  --marker-warning: #f39c12;
  --marker-danger: #e74c3c;
  --marker-info: #9b59b6;
}

.task-info-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 300px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1000;
  animation: slide-in 0.3s ease-out;
}

@keyframes slide-in {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.task-info-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--marker-primary);
  color: white;
}

.task-info-header i {
  font-size: 1.2rem;
  margin-right: 10px;
}

.task-info-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.task-info-content {
  padding: 15px;
}

.task-info-section {
  margin-bottom: 15px;
}

.task-info-section:last-child {
  margin-bottom: 0;
}

.task-info-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-bottom: 5px;
  color: #34495e;
}

.task-info-label i {
  margin-right: 8px;
  color: #7f8c8d;
}

.task-info-value {
  padding: 5px 0;
}

.task-info-coordinates {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-family: monospace;
}

.task-info-route {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 5px 0;
}

.route-point {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.route-point i {
  color: #7f8c8d;
}

.route-arrow {
  display: flex;
  justify-content: center;
  padding: 2px 0;
  color: #7f8c8d;
}

.task-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  width: 100%;
}

.task-status.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}

.task-status.pending {
  background-color: rgba(52, 152, 219, 0.1);
  color: #2980b9;
}

.task-info-actions {
  display: flex;
  gap: 10px;
  padding: 0 15px 15px;
}

.task-info-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.task-info-button.primary {
  background-color: var(--marker-primary);
  color: white;
}

.task-info-button.primary:hover {
  background-color: #2980b9;
}

.task-info-button.secondary {
  background-color: #ecf0f1;
  color: #34495e;
}

.task-info-button.secondary:hover {
  background-color: #bdc3c7;
}

/* Responzivní design */
@media (max-width: 768px) {
  .task-info-panel {
    width: calc(100% - 40px);
    bottom: 10px;
    right: 10px;
    left: 10px;
  }
}
