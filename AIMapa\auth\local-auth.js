/**
 * Lokální autentizace pro vývoj
 * Verze 0.4.4
 */

const express = require('express');
const session = require('express-session');

class LocalAuth {
  constructor(options = {}) {
    this.secret = options.secret || 'local-dev-secret-key';
    this.sessionConfig = {
      secret: this.secret,
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: false, // pro HTTP v development
        maxAge: 24 * 60 * 60 * 1000 // 24 hodin
      }
    };
  }

  /**
   * Middleware pro session
   */
  getSessionMiddleware() {
    return session(this.sessionConfig);
  }

  /**
   * Middleware pro kontrolu autentizace
   */
  requireAuth(req, res, next) {
    if (req.session && req.session.user) {
      return next();
    } else {
      return res.status(401).json({ error: 'Vyžadována autentizace' });
    }
  }

  /**
   * Middleware pro volitelnou autentizace
   */
  optionalAuth(req, res, next) {
    // <PERSON><PERSON><PERSON>, ale nastav user pokud existuje
    req.user = req.session?.user || null;
    next();
  }

  /**
   * Vytvoření auth routes
   */
  createRoutes() {
    const router = express.Router();

    // Přihl<PERSON>šení
    router.post('/login', (req, res) => {
      const { username, password } = req.body;
      
      // Jednoduchá validace pro vývoj
      if (username && password) {
        req.session.user = {
          id: 'local-user-1',
          username: username,
          email: `${username}@local.dev`,
          name: username,
          picture: 'https://via.placeholder.com/150',
          roles: ['user'],
          created_at: new Date().toISOString()
        };
        
        res.json({
          success: true,
          user: req.session.user,
          message: 'Úspěšně přihlášen'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Vyplňte uživatelské jméno a heslo'
        });
      }
    });

    // Odhlášení
    router.post('/logout', (req, res) => {
      req.session.destroy((err) => {
        if (err) {
          return res.status(500).json({
            success: false,
            error: 'Chyba při odhlašování'
          });
        }
        
        res.json({
          success: true,
          message: 'Úspěšně odhlášen'
        });
      });
    });

    // Získání informací o uživateli
    router.get('/user', (req, res) => {
      if (req.session && req.session.user) {
        res.json({
          success: true,
          user: req.session.user
        });
      } else {
        res.status(401).json({
          success: false,
          error: 'Nepřihlášen'
        });
      }
    });

    // Registrace (pro vývoj)
    router.post('/register', (req, res) => {
      const { username, password, email } = req.body;
      
      if (username && password) {
        req.session.user = {
          id: `local-user-${Date.now()}`,
          username: username,
          email: email || `${username}@local.dev`,
          name: username,
          picture: 'https://via.placeholder.com/150',
          roles: ['user'],
          created_at: new Date().toISOString()
        };
        
        res.json({
          success: true,
          user: req.session.user,
          message: 'Úspěšně zaregistrován a přihlášen'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Vyplňte uživatelské jméno a heslo'
        });
      }
    });

    return router;
  }

  /**
   * Získání uživatele z session
   */
  getUser(req) {
    return req.session?.user || null;
  }

  /**
   * Kontrola, zda je uživatel přihlášen
   */
  isAuthenticated(req) {
    return !!(req.session && req.session.user);
  }
}

module.exports = LocalAuth;
