/**
 * Test Groq Provider
 * Verze 0.4.4
 */

require('dotenv').config();
const GroqProvider = require('./llm-providers/groq-provider');

async function testGroq() {
  console.log('🧪 Testování Groq Provider...\n');

  try {
    // Kontrola proměnných prostředí
    if (!process.env.GROQ_API_KEY) {
      throw new Error('GROQ_API_KEY není nastavena');
    }

    // Inicializace providera
    const provider = new GroqProvider({
      apiKey: process.env.GROQ_API_KEY,
      model: process.env.GROQ_MODEL || 'llama3-8b-8192',
      temperature: 0.7,
      maxTokens: 500
    });

    console.log('✅ Provider inicializován');
    console.log('📋 Informace o modelu:', provider.getModelInfo());
    console.log('🎯 Podporované modely:', GroqProvider.getSupportedModels().join(', '));
    console.log();

    // Test připojení
    console.log('🔗 Testování připojení...');
    const connectionTest = await provider.testConnection();
    console.log(`${connectionTest ? '✅' : '❌'} Připojení: ${connectionTest ? 'úspěšné' : 'neúspěšné'}`);
    console.log();

    if (!connectionTest) {
      console.log('❌ Test ukončen kvůli neúspěšnému připojení');
      return;
    }

    // Test základního dotazu
    console.log('💬 Testování základního dotazu...');
    const prompt = process.argv[2] || 'Jak se dostanu z Prahy do Brna?';
    console.log(`📝 Prompt: "${prompt}"`);
    
    const startTime = Date.now();
    const response = await provider.getCompletion(prompt);
    const endTime = Date.now();

    console.log(`⏱️  Doba odezvy: ${endTime - startTime}ms (Groq je známý rychlostí!)`);
    console.log(`📊 Použité tokeny: ${response.usage.totalTokens} (prompt: ${response.usage.promptTokens}, odpověď: ${response.usage.completionTokens})`);
    console.log(`🤖 Model: ${response.model}`);
    console.log(`🏢 Provider: ${response.provider}`);
    console.log();
    console.log('💭 Odpověď:');
    console.log('─'.repeat(50));
    console.log(response.text);
    console.log('─'.repeat(50));
    console.log();

    // Test získání seznamu modelů
    console.log('📋 Testování získání seznamu modelů...');
    try {
      const models = await provider.getAvailableModels();
      console.log(`✅ Nalezeno ${models.length} modelů:`);
      models.slice(0, 10).forEach(model => {
        console.log(`  - ${model.id} (${model.owned_by})`);
      });
      if (models.length > 10) {
        console.log(`  ... a dalších ${models.length - 10} modelů`);
      }
    } catch (error) {
      console.log(`❌ Chyba při získávání modelů: ${error.message}`);
    }

    // Test rychlosti s více dotazy
    console.log('\n⚡ Testování rychlosti Groq (3 rychlé dotazy)...');
    const quickPrompts = [
      'Co je to AI?',
      'Jaké je hlavní město Francie?',
      'Kolik je 2+2?'
    ];

    for (let i = 0; i < quickPrompts.length; i++) {
      const start = Date.now();
      const quickResponse = await provider.getCompletion(quickPrompts[i]);
      const end = Date.now();
      console.log(`  ${i + 1}. "${quickPrompts[i]}" → ${end - start}ms`);
    }

    console.log('\n✅ Test Groq Provider dokončen úspěšně!');
    console.log('💡 Groq je optimalizován pro rychlost - ideální pro real-time aplikace!');

  } catch (error) {
    console.error('❌ Chyba při testování Groq Provider:', error.message);
    console.error('\n💡 Zkontrolujte:');
    console.error('   - GROQ_API_KEY je správně nastavena');
    console.error('   - Máte dostatečné kvóty na Groq');
    console.error('   - Model je podporován');
    process.exit(1);
  }
}

// Spuštění testu
if (require.main === module) {
  testGroq();
}

module.exports = testGroq;
