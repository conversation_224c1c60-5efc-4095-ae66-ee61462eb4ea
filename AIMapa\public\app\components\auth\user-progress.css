/**
 * Styly pro systém XP a achievementů
 * Verze 0.2.8.6.6
 */

/* <PERSON>obrazen<PERSON> postupu u<PERSON> */
.user-progress-display {
    position: fixed;
    top: 10px;
    left: 10px; /* <PERSON>m<PERSON><PERSON> z right na left, aby se ne<PERSON><PERSON><PERSON> s jin<PERSON><PERSON> prvky */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 120px;
}

.user-progress-level {
    font-weight: bold;
    text-align: center;
}

.user-progress-xp-bar {
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
}

.user-progress-xp-fill {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.user-progress-xp-text {
    font-size: 10px;
    text-align: center;
    color: #666;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profilu */
.user-profile-button {
    position: fixed;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transition: background-color 0.3s;
}

.user-profile-button:hover {
    background-color: #388E3C;
}

.user-profile-button-icon {
    font-size: 20px;
}

/* Modal s profilem uživatele */
.user-profile-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.user-profile-modal.show {
    opacity: 1;
    pointer-events: auto;
}

.user-profile-modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.user-profile-modal.show .user-profile-modal-content {
    transform: scale(1);
}

.user-profile-modal-header {
    background-color: #4CAF50;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-profile-modal-header h2 {
    margin: 0;
    color: white;
    font-size: 1.5rem;
}

.user-profile-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

/* Záložky */
.user-profile-modal-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
}

.user-profile-modal-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.user-profile-tab-button {
    padding: 12px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    white-space: nowrap;
}

.user-profile-tab-button:hover {
    background-color: #eee;
    color: #333;
}

.user-profile-tab-button.active {
    border-bottom-color: #4CAF50;
    color: #4CAF50;
    font-weight: bold;
}

.user-profile-tab-content {
    display: none;
    padding: 20px;
    animation: fadeIn 0.3s;
}

.user-profile-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.user-profile-modal-body {
    overflow-y: auto;
    flex: 1;
}

/* Přehled */
.user-profile-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.user-profile-level-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.user-profile-level-circle {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.user-profile-level-number {
    font-size: 28px;
    font-weight: bold;
    color: white;
}

.user-profile-level-text {
    font-size: 14px;
    color: #666;
}

.user-profile-progress {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.user-profile-xp-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.user-profile-xp-bar {
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
}

.user-profile-xp-fill {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 5px;
    transition: width 0.3s ease;
}

.user-profile-xp-percentage {
    font-size: 14px;
    font-weight: bold;
    color: #4CAF50;
    text-align: right;
}

.user-profile-stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.user-profile-stats-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.user-profile-stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.user-profile-stats-card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.user-profile-stats-card-value {
    font-size: 24px;
    font-weight: bold;
    color: #4CAF50;
}

.user-profile-activity {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-activity-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.user-profile-activity-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 5px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.user-profile-activity-icon {
    font-size: 24px;
    color: #4CAF50;
}

.user-profile-activity-info {
    flex: 1;
}

.user-profile-activity-title {
    font-size: 14px;
    color: #666;
}

.user-profile-activity-value {
    font-size: 16px;
    font-weight: bold;
}

.user-profile-recent-xp {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
}

.user-profile-recent-xp-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.user-profile-recent-xp-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 5px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.user-profile-recent-xp-amount {
    font-weight: bold;
    color: #4CAF50;
}

.user-profile-recent-xp-reason {
    flex: 1;
    margin: 0 10px;
    font-size: 14px;
}

.user-profile-recent-xp-time {
    font-size: 12px;
    color: #999;
}

.user-profile-no-xp {
    text-align: center;
    padding: 15px;
    color: #999;
    font-style: italic;
}

/* Statistiky */
.user-profile-stats-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-stats-chart {
    margin-bottom: 15px;
    height: 200px;
}

.user-profile-stats-chart-container {
    width: 100%;
    height: 100%;
}

.user-profile-bar-chart {
    display: flex;
    height: 100%;
    align-items: flex-end;
    gap: 10px;
    padding-bottom: 30px;
}

.user-profile-bar-chart-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    position: relative;
}

.user-profile-bar-chart-label {
    position: absolute;
    bottom: -25px;
    font-size: 12px;
    color: #666;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-profile-bar-chart-bar-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}

.user-profile-bar-chart-bar {
    width: 70%;
    background-color: #4CAF50;
    border-radius: 3px 3px 0 0;
    transition: height 0.5s;
    min-height: 5px;
}

.user-profile-bar-chart-value {
    font-size: 12px;
    font-weight: bold;
    margin-top: 5px;
    color: #4CAF50;
}

.user-profile-stats-table {
    overflow-x: auto;
}

.user-profile-stats-table table {
    width: 100%;
    border-collapse: collapse;
}

.user-profile-stats-table th,
.user-profile-stats-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.user-profile-stats-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #333;
}

.user-profile-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.user-profile-stats-grid-item {
    background-color: white;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.user-profile-stats-grid-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.user-profile-stats-grid-value {
    font-size: 18px;
    font-weight: bold;
    color: #4CAF50;
}

.user-profile-no-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Achievementy */
.user-profile-achievements-summary {
    display: flex;
    align-items: center;
    gap: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-achievements-count {
    text-align: center;
}

.user-profile-achievements-count-value {
    font-size: 36px;
    font-weight: bold;
    color: #4CAF50;
}

.user-profile-achievements-count-label {
    font-size: 14px;
    color: #666;
}

.user-profile-achievements-progress {
    flex: 1;
}

.user-profile-achievements-progress-bar {
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
}

.user-profile-achievements-progress-fill {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 5px;
    transition: width 0.3s ease;
}

.user-profile-achievements-progress-text {
    font-size: 14px;
    color: #666;
    text-align: right;
}

.user-profile-achievements-tabs {
    display: flex;
    margin-bottom: 15px;
}

.user-profile-achievements-tab-button {
    flex: 1;
    padding: 10px;
    background-color: #f5f5f5;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
}

.user-profile-achievements-tab-button:first-child {
    border-radius: 5px 0 0 5px;
}

.user-profile-achievements-tab-button:last-child {
    border-radius: 0 5px 5px 0;
}

.user-profile-achievements-tab-button:hover {
    background-color: #eee;
    color: #333;
}

.user-profile-achievements-tab-button.active {
    background-color: #4CAF50;
    color: white;
    font-weight: bold;
}

.user-profile-achievements-tab-content {
    display: none;
    animation: fadeIn 0.3s;
}

.user-profile-achievements-tab-content.active {
    display: block;
}

.user-profile-achievements {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-profile-achievement {
    display: flex;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.user-profile-achievement:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.user-profile-achievement.locked {
    opacity: 0.7;
    background-color: #f0f0f0;
}

.user-profile-achievement-icon {
    font-size: 30px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: #e0e0e0;
    border-radius: 50%;
    flex-shrink: 0;
}

.user-profile-achievement:not(.locked) .user-profile-achievement-icon {
    background-color: #4CAF50;
    color: white;
}

.user-profile-achievement-info {
    flex: 1;
}

.user-profile-achievement-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.user-profile-achievement-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.user-profile-achievement-date {
    font-size: 12px;
    color: #999;
}

.user-profile-achievement-reward {
    font-size: 12px;
    color: #4CAF50;
    font-weight: bold;
}

.user-profile-no-achievements {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Historie XP */
.user-profile-xp-history-filters {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.user-profile-xp-history-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-profile-xp-history-filter label {
    font-size: 14px;
    color: #666;
}

.user-profile-xp-history-filter select {
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: white;
    flex: 1;
}

.user-profile-xp-history {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-profile-xp-history-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-profile-xp-history-amount {
    font-size: 18px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 80px;
    text-align: center;
}

.user-profile-xp-history-info {
    flex: 1;
}

.user-profile-xp-history-reason {
    font-size: 14px;
    margin-bottom: 5px;
}

.user-profile-xp-history-date {
    font-size: 12px;
    color: #999;
}

.user-profile-xp-history-category {
    font-size: 12px;
    color: white;
    background-color: #4CAF50;
    padding: 3px 8px;
    border-radius: 10px;
}

.user-profile-no-xp-history {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* Notifikace */
.user-progress-notification {
    position: fixed;
    bottom: 20px;
    left: 20px; /* Změna z right na left, aby se neprotínalo s jinými prvky */
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 2000;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 300px;
}

.user-progress-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.user-progress-notification-icon {
    font-size: 24px;
}

.user-progress-notification-content {
    flex: 1;
}

.user-progress-notification-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.user-progress-notification-text {
    font-size: 14px;
    color: #666;
}

/* Specifické styly pro různé typy notifikací */
.xp-notification .user-progress-notification-icon {
    color: #4CAF50;
}

.level-up-notification {
    background-color: #4CAF50;
    color: white;
}

.level-up-notification .user-progress-notification-text {
    color: rgba(255, 255, 255, 0.8);
}

.achievement-notification {
    background-color: #FFC107;
}

.achievement-notification .user-progress-notification-text {
    color: #333;
}

.daily-bonus-notification {
    background-color: #2196F3;
    color: white;
}

.daily-bonus-notification .user-progress-notification-text {
    color: rgba(255, 255, 255, 0.9);
}

.daily-bonus-notification .user-progress-notification-icon {
    color: #FFEB3B;
    font-size: 28px;
    animation: rotate 1s ease-in-out;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(15deg); }
    100% { transform: rotate(0deg); }
}

/* Tmavý režim */
body[data-theme="dark"] .user-progress-display {
    background-color: rgba(51, 51, 51, 0.9);
    color: white;
}

body[data-theme="dark"] .user-progress-xp-bar {
    background-color: #555;
}

body[data-theme="dark"] .user-progress-xp-text {
    color: #aaa;
}

body[data-theme="dark"] .user-profile-modal-content {
    background-color: #333;
    color: white;
}

body[data-theme="dark"] .user-profile-modal-tabs {
    background-color: #444;
    border-bottom-color: #555;
}

body[data-theme="dark"] .user-profile-tab-button {
    color: #aaa;
}

body[data-theme="dark"] .user-profile-tab-button:hover {
    background-color: #555;
    color: #eee;
}

body[data-theme="dark"] .user-profile-tab-button.active {
    color: #4CAF50;
}

body[data-theme="dark"] .user-profile-info,
body[data-theme="dark"] .user-profile-achievement,
body[data-theme="dark"] .user-profile-stats-card,
body[data-theme="dark"] .user-profile-activity,
body[data-theme="dark"] .user-profile-recent-xp,
body[data-theme="dark"] .user-profile-stats-section,
body[data-theme="dark"] .user-profile-achievements-summary,
body[data-theme="dark"] .user-profile-xp-history-filters,
body[data-theme="dark"] .user-profile-xp-history-item {
    background-color: #444;
}

body[data-theme="dark"] .user-profile-activity-item,
body[data-theme="dark"] .user-profile-recent-xp-item,
body[data-theme="dark"] .user-profile-stats-grid-item {
    background-color: #555;
}

body[data-theme="dark"] .user-profile-xp-bar,
body[data-theme="dark"] .user-profile-achievements-progress-bar {
    background-color: #555;
}

body[data-theme="dark"] .user-profile-level-text,
body[data-theme="dark"] .user-profile-xp-info,
body[data-theme="dark"] .user-profile-stats-card-title,
body[data-theme="dark"] .user-profile-activity-title,
body[data-theme="dark"] .user-profile-stats-grid-title,
body[data-theme="dark"] .user-profile-achievements-count-label,
body[data-theme="dark"] .user-profile-achievements-progress-text,
body[data-theme="dark"] .user-profile-xp-history-filter label,
body[data-theme="dark"] .user-profile-bar-chart-label {
    color: #aaa;
}

body[data-theme="dark"] .user-profile-xp-text,
body[data-theme="dark"] .user-profile-achievement-description,
body[data-theme="dark"] .user-profile-recent-xp-reason {
    color: #bbb;
}

body[data-theme="dark"] .user-profile-achievement-date,
body[data-theme="dark"] .user-profile-recent-xp-time,
body[data-theme="dark"] .user-profile-xp-history-date,
body[data-theme="dark"] .user-profile-no-achievements,
body[data-theme="dark"] .user-profile-no-xp,
body[data-theme="dark"] .user-profile-no-data,
body[data-theme="dark"] .user-profile-no-xp-history {
    color: #888;
}

body[data-theme="dark"] .user-profile-achievements-tab-button {
    background-color: #444;
    color: #aaa;
}

body[data-theme="dark"] .user-profile-achievements-tab-button:hover {
    background-color: #555;
    color: #eee;
}

body[data-theme="dark"] .user-profile-achievements-tab-button.active {
    background-color: #388E3C;
}

body[data-theme="dark"] .user-profile-achievement.locked {
    background-color: #3a3a3a;
}

body[data-theme="dark"] .user-profile-achievement-icon {
    background-color: #555;
}

body[data-theme="dark"] .user-profile-xp-history-filter select {
    background-color: #555;
    border-color: #666;
    color: #eee;
}

body[data-theme="dark"] .user-profile-stats-table th {
    background-color: #444;
    color: #eee;
}

body[data-theme="dark"] .user-profile-stats-table td {
    border-bottom-color: #555;
}

body[data-theme="dark"] .user-progress-notification {
    background-color: #333;
    color: white;
}

body[data-theme="dark"] .user-progress-notification-text {
    color: #aaa;
}

body[data-theme="dark"] .level-up-notification {
    background-color: #388E3C;
}

body[data-theme="dark"] .achievement-notification {
    background-color: #FFA000;
    color: #333;
}

body[data-theme="dark"] .daily-bonus-notification {
    background-color: #1976D2;
}

/* Responzivní design */
@media (max-width: 768px) {
    .user-progress-display {
        right: 60px;
        min-width: 100px;
    }

    .user-profile-info {
        flex-direction: column;
    }

    .user-profile-stats-summary,
    .user-profile-activity-stats,
    .user-profile-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .user-profile-achievements-summary {
        flex-direction: column;
    }

    .user-profile-achievement {
        flex-direction: column;
    }

    .user-profile-achievement-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .user-profile-xp-history-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-profile-xp-history-amount {
        margin-bottom: 10px;
    }

    .user-profile-xp-history-category {
        align-self: flex-start;
        margin-top: 10px;
    }

    .user-profile-recent-xp-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-profile-recent-xp-amount {
        margin-bottom: 5px;
    }

    .user-profile-recent-xp-reason {
        margin: 0 0 5px 0;
    }
}

@media (max-width: 480px) {
    .user-profile-stats-summary,
    .user-profile-activity-stats,
    .user-profile-stats-grid {
        grid-template-columns: 1fr;
    }

    .user-profile-modal-content {
        width: 95%;
        max-width: none;
    }

    .user-profile-tab-button {
        padding: 10px;
        font-size: 14px;
    }

    .user-profile-modal-body {
        padding: 15px 10px;
    }

    .user-profile-level-circle {
        width: 60px;
        height: 60px;
    }

    .user-profile-level-number {
        font-size: 24px;
    }
}
