/**
 * Test Azure OpenAI Provider
 * Verze 0.4.4
 */

require('dotenv').config();
const AzureOpenAIProvider = require('./llm-providers/azure-openai-provider');

async function testAzureOpenAI() {
  console.log('🧪 Testování Azure OpenAI Provider...\n');

  try {
    // Kontrola proměnných prostředí
    if (!process.env.AZURE_OPENAI_API_KEY) {
      throw new Error('AZURE_OPENAI_API_KEY není nastavena');
    }

    if (!process.env.AZURE_OPENAI_ENDPOINT) {
      throw new Error('AZURE_OPENAI_ENDPOINT není nastavena');
    }

    // Inicializace providera
    const provider = new AzureOpenAIProvider({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      model: process.env.AZURE_OPENAI_MODEL || 'gpt-4',
      apiVersion: process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview',
      temperature: 0.7,
      maxTokens: 500
    });

    console.log('✅ Provider inicializován');
    console.log('📋 Informace o modelu:', provider.getModelInfo());
    console.log();

    // Test připojení
    console.log('🔗 Testování připojení...');
    const connectionTest = await provider.testConnection();
    console.log(`${connectionTest ? '✅' : '❌'} Připojení: ${connectionTest ? 'úspěšné' : 'neúspěšné'}`);
    console.log();

    if (!connectionTest) {
      console.log('❌ Test ukončen kvůli neúspěšnému připojení');
      return;
    }

    // Test základního dotazu
    console.log('💬 Testování základního dotazu...');
    const prompt = process.argv[2] || 'Jak se dostanu z Prahy do Brna?';
    console.log(`📝 Prompt: "${prompt}"`);
    
    const startTime = Date.now();
    const response = await provider.getCompletion(prompt);
    const endTime = Date.now();

    console.log(`⏱️  Doba odezvy: ${endTime - startTime}ms`);
    console.log(`📊 Použité tokeny: ${response.usage.totalTokens} (prompt: ${response.usage.promptTokens}, odpověď: ${response.usage.completionTokens})`);
    console.log(`🤖 Model: ${response.model}`);
    console.log(`🏢 Provider: ${response.provider}`);
    console.log();
    console.log('💭 Odpověď:');
    console.log('─'.repeat(50));
    console.log(response.text);
    console.log('─'.repeat(50));
    console.log();

    // Test získání seznamu modelů
    console.log('📋 Testování získání seznamu modelů...');
    try {
      const models = await provider.getAvailableModels();
      console.log(`✅ Nalezeno ${models.length} modelů:`);
      models.slice(0, 5).forEach(model => {
        console.log(`  - ${model.id} (${model.model}) - ${model.status}`);
      });
      if (models.length > 5) {
        console.log(`  ... a dalších ${models.length - 5} modelů`);
      }
    } catch (error) {
      console.log(`❌ Chyba při získávání modelů: ${error.message}`);
    }

    console.log('\n✅ Test Azure OpenAI Provider dokončen úspěšně!');

  } catch (error) {
    console.error('❌ Chyba při testování Azure OpenAI Provider:', error.message);
    console.error('\n💡 Zkontrolujte:');
    console.error('   - AZURE_OPENAI_API_KEY je správně nastavena');
    console.error('   - AZURE_OPENAI_ENDPOINT je správně nastavena');
    console.error('   - Model deployment existuje v Azure OpenAI');
    console.error('   - Máte dostatečné kvóty');
    process.exit(1);
  }
}

// Spuštění testu
if (require.main === module) {
  testAzureOpenAI();
}

module.exports = testAzureOpenAI;
