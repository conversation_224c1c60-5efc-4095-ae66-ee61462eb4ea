/**
 * AIMapa - Inteligentní mapová aplikace
 *
 * Copyright (c) 2025 Jan <PERSON>ik
 *
 * UPOZORNĚNÍ: Tento software je chráněn autorskými právy.
 * Neoprávněné použití tohoto kódu bude mít za následek právní postih.
 * Více informací naleznete v souboru LICENSE.md.
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import EnhancedMapPage from './pages/EnhancedMapPage';
import DocsPage from './pages/DocsPage';
import SubscriptionPage from './pages/SubscriptionPage';
import TimelinePage from './pages/TimelinePage';
import './App.css';

// Import routingService a konfigurace
import routingService from './services/RoutingService';
import { MAP_CONFIG } from './config/mapConfig';

// Inicializace routingService s API klíčem
if (MAP_CONFIG.openRouteService.apiKey) {
  routingService.setApiKey(MAP_CONFIG.openRouteService.apiKey);
  console.log('OpenRouteService API key initialized');
} else {
  console.warn('OpenRouteService API key is missing. Some features might not work.');
}

// Komponenty stránek
const HomePage: React.FC = () => (
  <div className="page home-page">
    <h1>AI Mapa</h1>
    <p>Vítejte v aplikaci AI Mapa!</p>

    <div className="features-section">
      <h2>Funkce aplikace</h2>
      <div className="features-grid">
        <div className="feature-card">
          <i className="fas fa-map-marked-alt"></i>
          <h3>Interaktivní mapa</h3>
          <p>Prozkoumejte místa pomocí různých mapových podkladů</p>
        </div>
        <div className="feature-card">
          <i className="fas fa-robot"></i>
          <h3>AI asistent</h3>
          <p>Získejte pomoc s navigací a informace o místech</p>
        </div>
        <div className="feature-card">
          <i className="fas fa-route"></i>
          <h3>Plánování tras</h3>
          <p>Naplánujte si cestu mezi různými body</p>
        </div>
        <div className="feature-card">
          <i className="fas fa-search-location"></i>
          <h3>Pokročilé vyhledávání</h3>
          <p>Najděte místa pomocí přirozeného jazyka</p>
        </div>
      </div>
    </div>
  </div>
);

const ProfilePage: React.FC = () => (
  <div className="page profile-page">
    <h1>Profil</h1>
    <p>Zde bude profil uživatele.</p>
  </div>
);

// Hlavní komponenta
function App() {
  return (
    <Router>
      <div className="App">
        <header className="App-header">
          <div className="logo">
            <Link to="/">AIMapa</Link>
          </div>
          <nav className="main-nav">
            <ul>
              <li><Link to="/">Domů</Link></li>
              <li><Link to="/map">Mapa</Link></li>
              <li><Link to="/timeline">Časová osa</Link></li>
              <li><Link to="/profile">Profil</Link></li>
              <li><Link to="/subscription">Předplatné</Link></li>
              <li><Link to="/docs">Dokumentace</Link></li>
            </ul>
          </nav>
          <div className="auth-status">
            <button className="login-button">Přihlásit se</button>
          </div>
        </header>

        <main className="App-main">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/map" element={<EnhancedMapPage />} />
            <Route path="/timeline" element={<TimelinePage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/subscription" element={<SubscriptionPage />} />
            <Route path="/docs" element={<DocsPage />} />
          </Routes>
        </main>

        <footer className="App-footer">
          <p>&copy; 2025 AIMapa | Jan Lazorik | Všechna práva vyhrazena. Neoprávněné použití tohoto softwaru bude mít za následek právní postih.</p>
        </footer>
      </div>
    </Router>
  );
}

export default App;
