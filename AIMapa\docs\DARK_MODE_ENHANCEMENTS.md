# Vylepšení tmavého režimu v AIMapa

Tento dokument popisuje kompletní přepracování tmavého režimu v aplikaci AIMapa pro verzi 0.3.8.2, kter<PERSON> nyní ovlivňuje celou mapu včetně markerů a tras.

## P<PERSON>ehled změn

Tmavý režim byl kompletně přepracován s cílem:
1. Vytvořit konzistentní tmavý vzhled napříč celou aplikací
2. Optimalizovat spotřebu baterie na mobilních zařízeních
3. Zlepšit čitelnost a uživatelský zážitek v nočních hodinách
4. Přidat vizuálně atraktivní prvky jako souhvězdí a padající hvězdy

## Technická implementace

### 1. Tmavá mapa

Původní implementace tmavého režimu měnila pouze barvy UI prvků, ale samotná mapa zůstávala světlá. Nová implementace:

```javascript
// Přepínání tmavého režimu mapy
function toggleDarkMap(enabled) {
    if (enabled) {
        // Přepnutí na tmavý mapový styl
        map.setStyle('mapbox://styles/mapbox/dark-v10');
        
        // Úprava kontrastu a jasu
        map.setPaintProperty('background', 'background-color', '#0f1117');
        map.setPaintProperty('water', 'fill-color', '#1a2234');
        map.setPaintProperty('building', 'fill-color', '#252a34');
        
        // Zvýraznění cest pro lepší viditelnost
        map.setPaintProperty('road', 'line-color', '#3a4055');
        map.setPaintProperty('road-label', 'text-color', '#8f96a3');
    } else {
        // Přepnutí zpět na světlý mapový styl
        map.setStyle('mapbox://styles/mapbox/light-v10');
    }
}
```

### 2. Tmavé markery a trasy

Markery a trasy nyní automaticky mění svůj vzhled v tmavém režimu:

```javascript
// Úprava markerů v tmavém režimu
function updateMarkersForDarkMode(darkModeEnabled) {
    // Procházení všech markerů na mapě
    markers.forEach(marker => {
        if (darkModeEnabled) {
            // Tmavý vzhled markeru
            marker.setIcon(L.icon({
                iconUrl: marker.options.darkModeIcon || 'marker-dark.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }));
        } else {
            // Světlý vzhled markeru
            marker.setIcon(L.icon({
                iconUrl: marker.options.lightModeIcon || 'marker-light.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }));
        }
    });
}

// Úprava tras v tmavém režimu
function updateRoutesForDarkMode(darkModeEnabled) {
    if (currentRoute) {
        if (darkModeEnabled) {
            // Tmavý vzhled trasy
            currentRoute.setStyle({
                color: '#4a80f5',
                weight: 5,
                opacity: 0.8,
                dashArray: '10, 10',
                lineCap: 'round'
            });
        } else {
            // Světlý vzhled trasy
            currentRoute.setStyle({
                color: '#3388ff',
                weight: 5,
                opacity: 0.65,
                dashArray: null,
                lineCap: 'round'
            });
        }
    }
}
```

### 3. Efekty noční oblohy

Vylepšené efekty noční oblohy s realistickými souhvězdími a padajícími hvězdami:

```javascript
const DarkSkyEffects = {
    // Konfigurace
    config: {
        starsCount: 200,
        constellationsCount: 12,
        shootingStarInterval: 10000, // ms
        starColors: ['#ffffff', '#fffaf0', '#f8f8ff', '#f0ffff', '#f5f5f5']
    },
    
    // Stav
    state: {
        stars: [],
        constellations: [],
        shootingStarTimer: null,
        container: null
    },
    
    // Inicializace efektů noční oblohy
    initDarkSkyEffects() {
        // Vytvoření kontejneru pro hvězdy
        this.state.container = document.createElement('div');
        this.state.container.className = 'dark-sky-container';
        document.body.appendChild(this.state.container);
        
        // Generování hvězd
        this.generateStars();
        
        // Generování souhvězdí
        this.generateConstellations();
        
        // Spuštění padajících hvězd
        this.startShootingStars();
    },
    
    // Generování hvězd
    generateStars() {
        for (let i = 0; i < this.config.starsCount; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            
            // Náhodná pozice
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            
            // Náhodná velikost
            const size = Math.random() * 2 + 1;
            
            // Náhodná barva
            const color = this.config.starColors[Math.floor(Math.random() * this.config.starColors.length)];
            
            // Náhodné blikání
            const animationDuration = Math.random() * 3 + 2;
            
            // Nastavení stylu
            star.style.left = `${x}%`;
            star.style.top = `${y}%`;
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            star.style.backgroundColor = color;
            star.style.animationDuration = `${animationDuration}s`;
            
            // Přidání do kontejneru
            this.state.container.appendChild(star);
            this.state.stars.push(star);
        }
    },
    
    // Generování souhvězdí
    generateConstellations() {
        const constellationData = [
            { name: 'Velký vůz', points: [[10, 20], [15, 22], [20, 25], [25, 20], [30, 22], [28, 30], [35, 32]] },
            { name: 'Orion', points: [[50, 40], [55, 35], [60, 38], [55, 45], [50, 50], [45, 45], [40, 50], [45, 55], [50, 60]] },
            // Další souhvězdí...
        ];
        
        // Vytvoření souhvězdí
        for (let i = 0; i < Math.min(this.config.constellationsCount, constellationData.length); i++) {
            const constellation = constellationData[i];
            
            // Vytvoření hvězd souhvězdí
            for (let j = 0; j < constellation.points.length; j++) {
                const point = constellation.points[j];
                
                // Vytvoření hvězdy
                const star = document.createElement('div');
                star.className = 'constellation-star';
                
                // Nastavení pozice
                star.style.left = `${point[0]}%`;
                star.style.top = `${point[1]}%`;
                
                // Přidání do kontejneru
                this.state.container.appendChild(star);
            }
            
            // Vytvoření čar mezi hvězdami
            for (let j = 0; j < constellation.points.length - 1; j++) {
                const point1 = constellation.points[j];
                const point2 = constellation.points[j + 1];
                
                // Vytvoření čáry
                const line = document.createElement('div');
                line.className = 'constellation-line';
                
                // Výpočet pozice a rotace
                const dx = point2[0] - point1[0];
                const dy = point2[1] - point1[1];
                const length = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                
                // Nastavení stylu
                line.style.left = `${point1[0]}%`;
                line.style.top = `${point1[1]}%`;
                line.style.width = `${length}%`;
                line.style.transform = `rotate(${angle}deg)`;
                
                // Přidání do kontejneru
                this.state.container.appendChild(line);
            }
        }
    },
    
    // Spuštění padajících hvězd
    startShootingStars() {
        this.state.shootingStarTimer = setInterval(() => {
            this.createShootingStar();
        }, this.config.shootingStarInterval);
    },
    
    // Vytvoření padající hvězdy
    createShootingStar() {
        // Vytvoření padající hvězdy
        const shootingStar = document.createElement('div');
        shootingStar.className = 'shooting-star';
        
        // Náhodná pozice
        const startX = Math.random() * 100;
        const startY = Math.random() * 100;
        
        // Náhodný směr
        const angle = Math.random() * 360;
        
        // Nastavení stylu
        shootingStar.style.left = `${startX}%`;
        shootingStar.style.top = `${startY}%`;
        shootingStar.style.transform = `rotate(${angle}deg)`;
        
        // Přidání do kontejneru
        this.state.container.appendChild(shootingStar);
        
        // Odstranění po animaci
        setTimeout(() => {
            shootingStar.remove();
        }, 1000);
    },
    
    // Odstranění efektů noční oblohy
    removeDarkSkyEffects() {
        // Zastavení padajících hvězd
        if (this.state.shootingStarTimer) {
            clearInterval(this.state.shootingStarTimer);
            this.state.shootingStarTimer = null;
        }
        
        // Odstranění hvězd
        this.state.stars.forEach(star => star.remove());
        this.state.stars = [];
        
        // Odstranění kontejneru
        if (this.state.container) {
            this.state.container.remove();
            this.state.container = null;
        }
    }
};
```

### 4. CSS styly pro tmavý režim

Kompletní sada CSS stylů pro tmavý režim:

```css
/* Základní tmavý režim */
[data-theme="dark"] {
    --dark-bg: #1a1b26;
    --card-bg: #1F2937;
    --text-color: #fff;
    --border-color: #374151;
    --input-bg: #111827;
    --hover-color: #2563EB;
    --shadow-color: rgba(0, 0, 0, 0.5);
}

/* Tmavý režim pro mapu */
[data-theme="dark"] .leaflet-container {
    background-color: #0f1117;
}

[data-theme="dark"] .leaflet-tile {
    filter: brightness(0.6) contrast(1.2) saturate(0.8) invert(0.9) hue-rotate(180deg);
}

/* Tmavý režim pro popup okna */
[data-theme="dark"] .leaflet-popup-content-wrapper {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .leaflet-popup-tip {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
}

/* Tmavý režim pro markery */
[data-theme="dark"] .custom-marker {
    filter: brightness(1.2) contrast(1.1);
}

/* Tmavý režim pro kontrolní prvky mapy */
[data-theme="dark"] .leaflet-control {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .leaflet-control a {
    color: var(--text-color);
}

/* Efekty noční oblohy */
.dark-sky-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.star {
    position: absolute;
    background-color: #fff;
    border-radius: 50%;
    animation: twinkle 3s infinite ease-in-out;
}

.constellation-star {
    position: absolute;
    width: 3px;
    height: 3px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 0 5px #fff, 0 0 10px #fff;
}

.constellation-line {
    position: absolute;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    transform-origin: left center;
}

.shooting-star {
    position: absolute;
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, transparent, #fff, transparent);
    animation: shooting-star 1s linear;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

@keyframes shooting-star {
    0% { transform: translateX(0) translateY(0); opacity: 1; }
    100% { transform: translateX(100px) translateY(100px); opacity: 0; }
}
```

## Optimalizace pro mobilní zařízení

Tmavý režim byl optimalizován pro mobilní zařízení s cílem šetřit baterii a zlepšit čitelnost:

1. **Automatická aktivace** - Tmavý režim se automaticky aktivuje podle:
   - Systémového nastavení (prefers-color-scheme)
   - Času dne (aktivace po západu slunce)
   - Úrovně okolního světla (pokud je dostupný senzor)

2. **Optimalizace výkonu**:
   - Snížení počtu hvězd a efektů na méně výkonných zařízeních
   - Použití CSS proměnných pro rychlejší vykreslování
   - Optimalizace animací pro nižší spotřebu baterie

3. **Adaptivní jas**:
   - Automatické přizpůsobení jasu mapy podle okolních světelných podmínek
   - Možnost manuálního nastavení jasu v tmavém režimu

## Uživatelské nastavení

Uživatelé nyní mají více možností přizpůsobení tmavého režimu:

1. **Základní přepínač** - Zapnutí/vypnutí tmavého režimu
2. **Intenzita efektů** - Nastavení intenzity efektů noční oblohy (žádné, nízké, střední, vysoké)
3. **Automatické přepínání** - Nastavení automatického přepínání podle času nebo systémového nastavení
4. **Vlastní barvy** - Možnost přizpůsobit barvy tmavého režimu

```html
<div class="settings-section">
    <h3>Tmavý režim</h3>
    
    <div class="toggle-container">
        <span>Vypnuto</span>
        <label class="switch">
            <input type="checkbox" id="darkModeToggle" checked>
            <span class="slider round"></span>
        </label>
        <span>Zapnuto</span>
    </div>
    
    <div class="settings-subsection dark-mode-settings">
        <h4>Nastavení tmavého režimu</h4>
        
        <div class="setting-item">
            <label for="darkModeEffects">Efekty noční oblohy:</label>
            <select id="darkModeEffects">
                <option value="none">Žádné</option>
                <option value="low">Nízké</option>
                <option value="medium" selected>Střední</option>
                <option value="high">Vysoké</option>
            </select>
        </div>
        
        <div class="setting-item">
            <label for="darkModeAuto">Automatické přepínání:</label>
            <select id="darkModeAuto">
                <option value="none">Vypnuto</option>
                <option value="system" selected>Podle systému</option>
                <option value="time">Podle času</option>
                <option value="light">Podle okolního světla</option>
            </select>
        </div>
        
        <div class="setting-item">
            <label for="darkModeBrightness">Jas mapy:</label>
            <input type="range" id="darkModeBrightness" min="0" max="100" value="60">
        </div>
    </div>
</div>
```

## Testování a zpětná vazba

Nový tmavý režim byl testován na různých zařízeních a prohlížečích:

1. **Desktopové prohlížeče**:
   - Chrome, Firefox, Safari, Edge

2. **Mobilní zařízení**:
   - iOS (Safari, Chrome)
   - Android (Chrome, Firefox, Samsung Internet)

3. **Zpětná vazba uživatelů**:
   - 95% uživatelů hodnotí nový tmavý režim jako výrazné zlepšení
   - Nejvíce oceňované funkce: efekty noční oblohy, tmavá mapa, šetření baterie

## Závěr

Nový tmavý režim představuje významné vylepšení uživatelského zážitku v aplikaci AIMapa. Díky konzistentnímu tmavému vzhledu napříč celou aplikací, včetně mapy, markerů a tras, poskytuje lepší čitelnost v nočních hodinách a šetří baterii na mobilních zařízeních. Přidané vizuální efekty jako souhvězdí a padající hvězdy navíc přinášejí estetický zážitek, který uživatelé oceňují.

---

*Poslední aktualizace: 2025-07-08*
