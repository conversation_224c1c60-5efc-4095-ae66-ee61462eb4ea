/**
 * Azure OpenAI Provider
 * Verze 0.4.4
 *
 * Provider pro Azure OpenAI Service
 */

/**
 * T<PERSON><PERSON>da pro komunikaci s Azure OpenAI API
 */
class AzureOpenAIProvider {
  /**
   * Konstruktor
   * @param {Object} options - Konfigurační objekt
   * @param {string} options.apiKey - Azure OpenAI API klíč
   * @param {string} options.endpoint - Azure OpenAI endpoint URL
   * @param {string} options.model - Název deployment modelu
   * @param {string} options.apiVersion - Verze API
   * @param {number} options.temperature - Teplota (0.0 - 1.0)
   * @param {number} options.maxTokens - Maximální počet tokenů
   */
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.endpoint = options.endpoint;
    this.model = options.model || 'gpt-4';
    this.apiVersion = options.apiVersion || '2024-02-15-preview';
    this.temperature = options.temperature || 0.7;
    this.maxTokens = options.maxTokens || 1000;

    if (!this.apiKey) {
      throw new Error('Azure OpenAI API klíč je povinný');
    }

    if (!this.endpoint) {
      throw new Error('Azure OpenAI endpoint je povinný');
    }

    console.log(`Azure OpenAI Provider inicializován s modelem ${this.model}`);
  }

  /**
   * Získání odpovědi od Azure OpenAI
   * @param {string} prompt - Prompt pro model
   * @returns {Promise<Object>} Odpověď od modelu
   */
  async getCompletion(prompt) {
    try {
      const url = `${this.endpoint}/openai/deployments/${this.model}/chat/completions?api-version=${this.apiVersion}`;
      
      const requestBody = {
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Azure OpenAI API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('Azure OpenAI nevrátil žádnou odpověď');
      }

      return {
        text: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        model: this.model,
        provider: 'azure-openai'
      };
    } catch (error) {
      console.error('Chyba při komunikaci s Azure OpenAI:', error);
      throw error;
    }
  }

  /**
   * Získání seznamu dostupných modelů
   * @returns {Promise<Array>} Seznam dostupných modelů
   */
  async getAvailableModels() {
    try {
      const url = `${this.endpoint}/openai/deployments?api-version=${this.apiVersion}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'api-key': this.apiKey
        }
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Azure OpenAI API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();
      
      return data.data?.map(deployment => ({
        id: deployment.id,
        model: deployment.model,
        status: deployment.status,
        created: deployment.created_at
      })) || [];
    } catch (error) {
      console.error('Chyba při získávání seznamu modelů z Azure OpenAI:', error);
      throw error;
    }
  }

  /**
   * Test připojení k Azure OpenAI
   * @returns {Promise<boolean>} True pokud je připojení úspěšné
   */
  async testConnection() {
    try {
      await this.getCompletion('Test připojení');
      return true;
    } catch (error) {
      console.error('Test připojení k Azure OpenAI selhal:', error);
      return false;
    }
  }

  /**
   * Získání informací o modelu
   * @returns {Object} Informace o modelu
   */
  getModelInfo() {
    return {
      provider: 'azure-openai',
      model: this.model,
      endpoint: this.endpoint,
      apiVersion: this.apiVersion,
      temperature: this.temperature,
      maxTokens: this.maxTokens
    };
  }
}

module.exports = AzureOpenAIProvider;
