<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMapa - Testova<PERSON><PERSON> str<PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        h1, h2, h3 {
            color: #6d28d9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }

        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        button {
            padding: 8px 16px;
            background-color: #6d28d9;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #5b21b6;
        }

        .results {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 4px;
            border-left: 4px solid #6d28d9;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-left-color: #10b981;
        }

        .error {
            border-left-color: #ef4444;
        }

        .nav-links {
            margin-top: 20px;
            display: flex;
            gap: 15px;
        }

        .nav-links a {
            color: #6d28d9;
            text-decoration: none;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AIMapa - Testovací stránka</h1>
        <p>Tato stránka slouží k testování různých funkcí aplikace AIMapa verze 0.3.8.5.</p>

        <div class="nav-links">
            <a href="/" target="_blank">Otevřít aplikaci</a>
            <a href="/browser-test-runner.html">Spustit testy v prohlížeči</a>
            <a href="https://github.com/l4zorik/AIMapa" target="_blank">GitHub repozitář</a>
        </div>

        <div class="test-section">
            <h2>Unit testy</h2>
            <p>Testy pro ověření jednotlivých komponent a funkcí.</p>

            <div class="test-buttons">
                <button onclick="window.location.href='/browser-test-runner.html#unit'">Spustit unit testy</button>
            </div>

            <div class="results">
                <p>Unit testy ověřují správnou funkčnost jednotlivých komponent aplikace:</p>
                <ul>
                    <li>Výpočet vzdálenosti mezi body</li>
                    <li>Parsování souřadnic</li>
                    <li>Validace souřadnic</li>
                </ul>
                <p>Pro spuštění testů z příkazové řádky použijte:</p>
                <pre>npm run test:unit</pre>
            </div>
        </div>

        <div class="test-section">
            <h2>Integrační testy</h2>
            <p>Testy pro ověření interakce mezi moduly a externími API.</p>

            <div class="test-buttons">
                <button onclick="window.location.href='/browser-test-runner.html#integration'">Spustit integrační testy</button>
            </div>

            <div class="results">
                <p>Integrační testy ověřují správnou interakci s externími API:</p>
                <ul>
                    <li>OpenStreetMap API</li>
                    <li>Google Maps API</li>
                    <li>Vyhledávání tras</li>
                    <li>Multimodální vyhledávání</li>
                </ul>
                <p>Pro spuštění testů z příkazové řádky použijte:</p>
                <pre>npm run test:integration</pre>
            </div>
        </div>

        <div class="test-section">
            <h2>End-to-end testy</h2>
            <p>Testy pro ověření celého workflow aplikace.</p>

            <div class="test-buttons">
                <button onclick="window.location.href='/browser-test-runner.html#e2e'">Spustit end-to-end testy</button>
            </div>

            <div class="results">
                <p>End-to-end testy ověřují celý workflow aplikace:</p>
                <ul>
                    <li>Vyhledání trasy mezi dvěma body</li>
                    <li>Vyhledání bodu zájmu a přidání do oblíbených</li>
                    <li>Změna mapového podkladu a přepnutí do glóbus režimu</li>
                    <li>Interakce s AI asistentem pro doporučení trasy</li>
                </ul>
                <p>Pro spuštění testů z příkazové řádky použijte:</p>
                <pre>npm run test:e2e</pre>
            </div>
        </div>

        <div class="test-section">
            <h2>AI model testy</h2>
            <p>Testy pro ověření AI modelu a jeho výkonu.</p>

            <div class="test-buttons">
                <button onclick="window.location.href='/browser-test-runner.html#ai'">Spustit AI model testy</button>
            </div>

            <div class="results">
                <p>AI model testy ověřují výkon AI modelu:</p>
                <ul>
                    <li>Klasifikace míst (Accuracy, Precision, Recall, F1-score)</li>
                    <li>Predikce času cesty (MAE, RMSE, R²)</li>
                    <li>Doporučení míst (Precision@k, NDCG)</li>
                    <li>Bias a fairness (Equal Opportunity, Statistical Parity)</li>
                </ul>
                <p>Pro spuštění testů z příkazové řádky použijte:</p>
                <pre>npm run test:ai</pre>
            </div>
        </div>

        <div class="test-section">
            <h2>Všechny testy</h2>
            <p>Spuštění všech testů najednou.</p>

            <div class="test-buttons">
                <button onclick="window.location.href='/browser-test-runner.html#all'">Spustit všechny testy</button>
            </div>

            <div class="results">
                <p>Pro spuštění všech testů z příkazové řádky použijte:</p>
                <pre>npm test</pre>
                <p>Nebo použijte skripty:</p>
                <ul>
                    <li>Linux/macOS: <code>./tests/run-tests.sh --all</code></li>
                    <li>Windows: <code>tests\run-tests.bat --all</code></li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Načtení testovacích skriptů
        function loadScript(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // Načtení všech testovacích skriptů
        Promise.all([
            loadScript('/tests/unit/map-utils.test.js'),
            loadScript('/tests/integration/map-api.test.js'),
            loadScript('/tests/e2e/map-workflow.test.js'),
            loadScript('/tests/ai/model-evaluation.test.js')
        ]).then(() => {
            console.log('Všechny testovací skripty byly načteny');
        }).catch(error => {
            console.error('Chyba při načítání testovacích skriptů:', error);
        });

        // Kontrola, zda je uživatel na správné stránce
        document.addEventListener('DOMContentLoaded', function() {
            // Kontrola, zda je uživatel na správné stránce
            if (window.location.pathname === '/tests/index.html' || window.location.pathname === '/tests/') {
                console.log('Testovací stránka byla načtena');
            } else {
                console.warn('Testovací stránka byla načtena z neočekávané cesty:', window.location.pathname);
            }
        });
    </script>
</body>
</html>
