{"version": 3, "file": "js/main.b21208263df11332c6a7.min.js", "mappings": "mBAMAA,SAASC,iBAAiB,oBAAoB,WAC5CC,QAAQC,IAAI,kCASZD,QAAQC,IAAI,4BALd,G", "sources": ["webpack://aimapa/./public/js/main.js"], "sourcesContent": ["/**\n * Hlavní JavaScript soubor\n * Verze *******\n */\n\n// Inicializace aplikace\ndocument.addEventListener('DOMContentLoaded', () => {\n  console.log('AIMapa aplikace inicializována');\n  \n  // Inicializace komponent\n  initComponents();\n});\n\n// Inicializace komponent\nfunction initComponents() {\n  // Zde budou inicializovány jednotlivé komponenty\n  console.log('Komponenty inicializovány');\n}\n\n// Export pro Webpack\nexport default {\n  init: () => {\n    console.log('AIMapa modul inicializován');\n  }\n};\n"], "names": ["document", "addEventListener", "console", "log"], "sourceRoot": ""}