# Voice Bot - <PERSON><PERSON><PERSON><PERSON> ovládání pro AIMapa

## <PERSON><PERSON><PERSON><PERSON>

Voice Bot je nová funkce aplikace AIMapa, kter<PERSON> umožňuje hlasové ovládání všech hlavních funkcí aplikace. Pomocí hlasových příkazů můžete ovládat mapu, komunikovat s AI asistentem a navigovat v aplikaci bez použití myši nebo klávesnice.

## Požadavky

- **Prohlížeč**: Chrome, Edge, Firefox nebo Safari (s podporou Web Speech API)
- **Mikrofon**: Funkční mikrofon připojený k počítači
- **Povolení**: Aplikace potřebuje povolení k přístupu k mikrofonu

## Aktivace Voice Bota

### Způsoby aktivace:
1. **Kliknutí na tlačítko**: Klikněte na modré tlačítko s mikrofonem v pravém horním rohu
2. **Klávesová zkratka**: Stiskněte `Ctrl + Shift + V`
3. **Hlasový příkaz**: Řekněte "zapnout hlasové ovládání" (pokud je již částečně aktivní)

### Indikátory stavu:
- **Modré tlačítko**: Voice bot je vypnutý
- **Červené tlačítko s animací**: Voice bot je aktivní a naslouchá
- **Indikátor stavu**: Zobrazuje aktuální stav ("Naslouchám...", "Připraven", "Neaktivní")

## Dostupné hlasové příkazy

### Ovládání mapy:
- **"přidat bod"** / **"přidat aktivitu"** / **"nový bod"** - Aktivuje režim přidávání bodů na mapu
- **"vypočítat trasu"** / **"najít cestu"** - Vypočítá trasu mezi existujícími body
- **"vymazat mapu"** / **"smazat mapu"** - Odstraní všechny body z mapy
- **"vytisknout mapu"** - Připraví mapu k tisku

### Navigace a zobrazení:
- **"fullscreen"** / **"celá obrazovka"** - Přepne do režimu celé obrazovky
- **"glóbus"** / **"3d režim"** - Přepne do 3D režimu glóbusu
- **"přiblížit"** / **"zoom in"** - Přiblíží mapu
- **"oddálit"** / **"zoom out"** - Oddálí mapu
- **"vycentrovat mapu"** - Vycentruje mapu na výchozí pozici

### AI asistent:
- **"alexa"** - Zobrazí informace o klubu Alexa
- **"otevírací doba"** / **"kdy je otevřeno"** - Zobrazí otevírací dobu obchodů
- Jakýkoliv jiný text bude odeslán do AI asistenta pro zpracování

### Nastavení a nápověda:
- **"nastavení"** / **"možnosti"** - Otevře nastavení aplikace
- **"nápověda"** / **"pomoc"** / **"co můžu říct"** - Zobrazí seznam dostupných příkazů

### Ovládání Voice Bota:
- **"stop"** / **"zastavit"** / **"pauza"** - Dočasně zastaví naslouchání
- **"vypnout hlasové ovládání"** - Úplně vypne Voice Bot

## Jak používat Voice Bot

### 1. První spuštění:
1. Klikněte na tlačítko mikrofonu nebo stiskněte `Ctrl + Shift + V`
2. Prohlížeč požádá o povolení přístupu k mikrofonu - klikněte na "Povolit"
3. Tlačítko se změní na červené a začne pulzovat - Voice Bot nyní naslouchá

### 2. Používání příkazů:
1. Počkejte, až indikátor zobrazí "Naslouchám..."
2. Řekněte příkaz jasně a zřetelně
3. Počkejte na potvrzení - Voice Bot řekne, co udělal
4. Voice Bot automaticky pokračuje v naslouchání

### 3. Komunikace s AI:
- Řekněte jakoukoliv větu, která není rozpoznána jako specifický příkaz
- AI asistent zpracuje vaši zprávu a odpoví
- Odpověď bude zobrazena v chatu a také přečtena nahlas

## Tipy pro nejlepší výsledky

### Kvalita rozpoznávání:
- **Mluvte jasně a zřetelně**
- **Používejte přirozený tempo řeči**
- **Minimalizujte hluk v pozadí**
- **Držte mikrofon v přiměřené vzdálenosti**

### Efektivní používání:
- **Naučte se základní příkazy** - jsou rychlejší než navigace myší
- **Používejte krátké, jasné příkazy**
- **Počkejte na dokončení předchozího příkazu**
- **V hlučném prostředí použijte tlačítko "stop" a pak znovu aktivujte**

## Řešení problémů

### Voice Bot nereaguje:
1. Zkontrolujte, zda je tlačítko červené (aktivní)
2. Ověřte povolení mikrofonu v prohlížeči
3. Zkuste restartovat Voice Bot (vypnout a zapnout)
4. Zkontrolujte, zda mikrofon funguje v jiných aplikacích

### Špatné rozpoznávání:
1. Mluvte pomaleji a zřetelněji
2. Snižte hluk v pozadí
3. Zkuste se přiblížit k mikrofonu
4. Restartujte prohlížeč

### Chyby mikrofonu:
1. Zkontrolujte připojení mikrofonu
2. Ověřte nastavení zvuku v systému
3. Restartujte prohlížeč a povolte přístup k mikrofonu znovu
4. Zkuste jiný prohlížeč

## Klávesové zkratky

- **`Ctrl + Shift + V`** - Zapnout/vypnout Voice Bot
- **`Ctrl + Shift + H`** - Zobrazit nápovědu s příkazy

## Pokročilé funkce

### Dlouhé stisknutí:
- Podržte tlačítko mikrofonu po dobu 1 sekundy pro zobrazení nápovědy

### Automatické restartování:
- Voice Bot se automaticky restartuje po chybě nebo přerušení
- Pokud se to nestane, klikněte na tlačítko znovu

### Integrace s AI:
- Všechny odpovědi AI asistenta jsou automaticky přečteny nahlas
- Můžete vést plnohodnotnou konverzaci pouze hlasem

## Bezpečnost a soukromí

- **Hlasová data se nezaznamenávají** - zpracování probíhá lokálně v prohlížeči
- **Žádná data se neodesílají na servery** kromě zpráv do AI asistenta
- **Mikrofon je aktivní pouze při naslouchání** - indikováno červeným tlačítkem

## Kompatibilita

### Podporované prohlížeče:
- ✅ Google Chrome (doporučeno)
- ✅ Microsoft Edge
- ✅ Mozilla Firefox
- ✅ Safari (macOS)

### Nepodporované prohlížeče:
- ❌ Internet Explorer
- ❌ Starší verze prohlížečů

## Často kladené otázky

**Q: Můžu používat Voice Bot v jiném jazyce?**
A: Momentálně je Voice Bot optimalizován pro češtinu. Podpora dalších jazyků bude přidána v budoucích verzích.

**Q: Funguje Voice Bot offline?**
A: Rozpoznávání řeči vyžaduje internetové připojení, ale základní funkce aplikace fungují offline.

**Q: Můžu přidat vlastní příkazy?**
A: V současné verzi není možné přidávat vlastní příkazy. Tato funkce je plánována do budoucna.

**Q: Proč Voice Bot někdy nereaguje?**
A: Nejčastější příčiny jsou hluk v pozadí, špatná kvalita mikrofonu nebo dočasné problémy s internetem.

## Podpora

Pokud máte problémy s Voice Botem nebo návrhy na vylepšení, kontaktujte podporu nebo vytvořte issue na GitHubu.

---

**Verze dokumentace**: 1.0  
**Datum aktualizace**: 2025-01-09  
**Kompatibilní s AIMapa**: v0.3.8.5+
