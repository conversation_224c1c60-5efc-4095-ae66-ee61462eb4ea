/* <PERSON><PERSON>lad<PERSON><PERSON> proměnné pro barvy */
:root {
  --primary-green: #2ecc71;
  --primary-green-dark: #27ae60;
  --primary-green-light: #a9dfbf;

  --primary-red: #e74c3c;
  --primary-red-dark: #c0392b;
  --primary-red-light: #f5b7b1;

  --primary-orange: #f39c12;
  --primary-orange-dark: #d35400;
  --primary-orange-light: #fad7a0;

  --primary-blue: #3498db;
  --primary-blue-dark: #2980b9;
  --primary-blue-light: #aed6f1;

  --dark-bg: #1e272e;
  --light-bg: #f5f6fa;
  --card-bg: #2c3e50;
  --card-hover: #34495e;

  --text-light: #ffffff;
  --text-dark: #2c3e50;
  --text-muted: #bdc3c7;

  --border-color: #7f8c8d;
  --shadow-color: rgba(0, 0, 0, 0.2);

  --user-bubble-bg: #3498db;
  --assistant-bubble-bg: #2c3e50;
  --system-bubble-bg: rgba(127, 140, 141, 0.2);
}

/* Hlavn<PERSON> kontejner */
.enhanced-chat-interface {
  background-color: var(--light-bg);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 8px 20px var(--shadow-color);
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modal overlay pro dialogy */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-in-out;
}

/* Hlavička chatu */
.chat-header {
  background-color: var(--primary-blue);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.chat-header h2 {
  margin: 0;
  color: var(--text-light);
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.chat-header h2::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z"/><path d="M7 9h10v2H7zm0-3h10v2H7zm0 6h7v2H7z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 8px;
}

/* Stav API */
.api-status {
  display: flex;
  align-items: center;
}

.api-connected, .api-disconnected {
  display: flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 15px;
  font-size: 12px;
}

.api-connected {
  background-color: rgba(46, 204, 113, 0.2);
  border: 1px solid var(--primary-green);
}

.api-disconnected {
  background-color: rgba(231, 76, 60, 0.2);
  border: 1px solid var(--primary-red);
}

.status-icon {
  margin-right: 5px;
}

.status-icon.connected {
  color: var(--primary-green);
}

.status-icon.disconnected {
  color: var(--primary-red);
}

.status-details {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}

.status-model {
  font-weight: bold;
  color: var(--text-light);
  font-size: 12px;
}

.status-provider {
  font-size: 11px;
  color: var(--text-muted);
}

.status-message {
  color: var(--primary-red);
  font-weight: bold;
  font-size: 12px;
}

/* Oblast zpráv */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  background-color: #f0f2f5;
  background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="20" height="20" fill="none"/><circle cx="3" cy="3" r="1" fill="%23e0e0e0"/><circle cx="13" cy="13" r="1" fill="%23e0e0e0"/><circle cx="3" cy="13" r="1" fill="%23e0e0e0"/><circle cx="13" cy="3" r="1" fill="%23e0e0e0"/></svg>');
  background-repeat: repeat;
}

/* Prázdný chat */
.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-dark);
  text-align: center;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  max-width: 500px;
  margin: auto;
  animation: fadeIn 0.5s ease-out;
}

.welcome-icon {
  margin-bottom: 16px;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
}

.empty-chat h3 {
  font-size: 24px;
  margin: 0 0 16px;
  color: var(--primary-blue);
  font-weight: 600;
}

.empty-chat p {
  max-width: 400px;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: #555;
}

.welcome-features {
  text-align: left;
  margin: 0 0 20px;
  padding-left: 20px;
}

.welcome-features li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 10px;
  color: #555;
}

.welcome-features li::before {
  content: '•';
  color: var(--primary-blue);
  font-weight: bold;
  position: absolute;
  left: -10px;
}

/* Zprávy */
.chat-message {
  max-width: 75%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin: 4px 0;
}

/* Zprávy s požadavkem na plán */
.chat-message.plan-request {
  cursor: pointer;
}

.chat-message.plan-request:hover {
  background-color: var(--primary-blue-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.message-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.quick-plan-button {
  background-color: var(--primary-green);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-plan-button:hover {
  background-color: var(--primary-green-dark);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Zprávy uživatele */
.chat-message.user {
  align-self: flex-end;
  background-color: var(--user-bubble-bg);
  color: var(--text-light);
  border-bottom-right-radius: 4px;
  margin-right: 8px;
  position: relative;
}

.chat-message.user::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: -8px;
  width: 16px;
  height: 16px;
  background-color: var(--user-bubble-bg);
  clip-path: polygon(0 0, 0% 100%, 100% 100%);
}

/* Zprávy asistenta */
.chat-message.assistant {
  align-self: flex-start;
  background-color: white;
  color: var(--text-dark);
  border-bottom-left-radius: 4px;
  margin-left: 8px;
  position: relative;
}

.chat-message.assistant::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 16px;
  height: 16px;
  background-color: white;
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
}

/* Avatary pro zprávy */
.chat-message.user::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -40px;
  width: 28px;
  height: 28px;
  background-color: #dfe6e9;
  border-radius: 50%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 70%;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chat-message.assistant::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -40px;
  width: 28px;
  height: 28px;
  background-color: #dfe6e9;
  border-radius: 50%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232ecc71"><path d="M21 10.12h-6.78l2.74-2.82c-2.73-2.7-7.15-2.8-9.88-.1-2.73 2.71-2.73 7.08 0 9.79s7.15 2.71 9.88 0C18.32 15.65 19 14.08 19 12.1h2c0 1.98-.88 4.55-2.64 6.29-3.51 3.48-9.21 3.48-12.72 0-3.5-3.47-3.53-9.11-.02-12.58s9.14-3.47 12.65 0L21 3v7.12zM12.5 8v4.25l3.5 2.08-.72 1.21L11 13V8h1.5z"/></svg>');
  background-size: 70%;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Systémové zprávy */
.chat-message.system {
  align-self: center;
  background-color: var(--system-bubble-bg);
  color: var(--text-dark);
  border: 1px solid rgba(127, 140, 141, 0.3);
  font-size: 14px;
  max-width: 70%;
  border-radius: 8px;
}

.chat-message.error {
  align-self: center;
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--primary-red-dark);
  border: 1px solid var(--primary-red-light);
  font-size: 14px;
  max-width: 70%;
  border-radius: 8px;
}

.chat-message.warning {
  align-self: center;
  background-color: rgba(243, 156, 18, 0.1);
  color: var(--primary-orange-dark);
  border: 1px solid var(--primary-orange-light);
  font-size: 14px;
  max-width: 70%;
  border-radius: 8px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.message-role {
  font-weight: 600;
  color: inherit;
}

.chat-message.user .message-role {
  color: rgba(255, 255, 255, 0.9);
}

.chat-message.assistant .message-role {
  color: var(--primary-blue);
}

.message-time {
  opacity: 0.7;
}

.message-content {
  line-height: 1.5;
  word-break: break-word;
  font-size: 15px;
}

/* Vstupní oblast */
.chat-input-container {
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid #e6e6e6;
  display: flex;
  gap: 10px;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border-radius: 24px;
  border: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  color: var(--text-dark);
  resize: none;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  transition: all 0.3s;
  font-size: 15px;
  line-height: 1.4;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) inset;
}

.chat-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  background-color: white;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.chat-input:disabled {
  background-color: #f1f1f1;
  cursor: not-allowed;
  color: #999;
}

.chat-input::placeholder {
  color: #aaa;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.send-button, .clear-button, .services-button, .plan-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.send-button {
  background-color: var(--primary-blue);
  color: var(--text-light);
}

.send-button:hover:not(:disabled) {
  background-color: var(--primary-blue-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.clear-button {
  background-color: #e0e0e0;
  color: #666;
}

.clear-button:hover:not(:disabled) {
  background-color: var(--primary-red);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.services-button {
  background-color: #e0e0e0;
  color: #666;
}

.services-button:hover:not(:disabled) {
  background-color: var(--primary-orange);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.plan-button {
  background-color: #e0e0e0;
  color: #666;
}

.plan-button:hover:not(:disabled) {
  background-color: var(--primary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.send-button:disabled, .clear-button:disabled, .services-button:disabled, .plan-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Spinner načítání */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--text-light);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Banner s chybou API */
.api-error-banner {
  position: absolute;
  bottom: 70px;
  left: 15px;
  right: 15px;
  background-color: var(--primary-red);
  color: var(--text-light);
  padding: 10px 15px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: slideUp 0.3s ease-in-out;
  z-index: 10;
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.api-error-banner i {
  font-size: 18px;
}

/* Informace o kreditu */
.api-credit-info {
  padding: 4px 8px;
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
}

.credit-bar {
  height: 4px;
  background-color: rgba(231, 76, 60, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 3px;
}

.credit-progress {
  height: 100%;
  background-color: var(--primary-green);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.credit-text {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: var(--text-muted);
}

/* Responzivní design */
@media (max-width: 768px) {
  .chat-message {
    max-width: 90%;
  }

  .chat-message.system,
  .chat-message.error,
  .chat-message.warning {
    max-width: 85%;
  }

  .chat-header {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .status-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
  }

  .status-model, .status-provider {
    font-size: 10px;
  }
}
