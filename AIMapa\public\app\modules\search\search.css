/**
 * AIMapa - Styly pro vyhledávání
 * Verze 0.3.8.6
 */

/* Kontejner pro vyhledávací pole */
.search-container {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    width: 300px;
    max-width: 90%;
    z-index: 1000;
}

/* Vyhledávací pole */
#searchInput {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-right: none;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Tlačítko vyhledávání */
.search-btn {
    padding: 10px 15px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.search-btn:hover {
    background-color: #2563eb;
}

/* <PERSON><PERSON><PERSON>dky vyhledávání */
.search-results {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    max-width: 90%;
    max-height: 300px;
    overflow-y: auto;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.search-results.visible {
    display: block;
}

.search-result-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background-color: #f5f5f5;
}

.search-result-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.search-result-address {
    font-size: 12px;
    color: #666;
}

/* Tmavý režim */
body[data-theme="dark"] #searchInput {
    background-color: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
}

body[data-theme="dark"] .search-btn {
    background-color: #3b82f6;
}

body[data-theme="dark"] .search-btn:hover {
    background-color: #2563eb;
}

body[data-theme="dark"] .search-results {
    background-color: #1f2937;
}

body[data-theme="dark"] .search-result-item {
    border-bottom-color: #4b5563;
}

body[data-theme="dark"] .search-result-item:hover {
    background-color: #374151;
}

body[data-theme="dark"] .search-result-name {
    color: #e5e7eb;
}

body[data-theme="dark"] .search-result-address {
    color: #9ca3af;
}
