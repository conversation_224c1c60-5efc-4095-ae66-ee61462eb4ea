/**
 * OpenRouter Provider
 * Verze 0.4.4
 *
 * Provider pro OpenRouter API - přístup k různým modelům
 */

/**
 * T<PERSON>ída pro komunikaci s OpenRouter API
 */
class OpenRouterProvider {
  /**
   * Konstruktor
   * @param {Object} options - Konfigurační objekt
   * @param {string} options.apiKey - OpenRouter API klíč
   * @param {string} options.model - Název modelu
   * @param {number} options.temperature - Teplota (0.0 - 1.0)
   * @param {number} options.maxTokens - Maximální počet tokenů
   */
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.model = options.model || 'openai/gpt-4o-mini';
    this.temperature = options.temperature || 0.7;
    this.maxTokens = options.maxTokens || 1000;
    this.baseUrl = 'https://openrouter.ai/api/v1';

    if (!this.apiKey) {
      throw new Error('OpenRouter API klíč je povinný');
    }

    console.log(`OpenRouter Provider inicializován s modelem ${this.model}`);
  }

  /**
   * Získání odpovědi od OpenRouter
   * @param {string} prompt - Prompt pro model
   * @returns {Promise<Object>} Odpověď od modelu
   */
  async getCompletion(prompt) {
    try {
      const url = `${this.baseUrl}/chat/completions`;
      
      const requestBody = {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': 'https://aimapa.netlify.app',
          'X-Title': 'AIMapa'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`OpenRouter API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('OpenRouter nevrátil žádnou odpověď');
      }

      return {
        text: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        model: this.model,
        provider: 'openrouter'
      };
    } catch (error) {
      console.error('Chyba při komunikaci s OpenRouter:', error);
      throw error;
    }
  }

  /**
   * Získání seznamu dostupných modelů
   * @returns {Promise<Array>} Seznam dostupných modelů
   */
  async getAvailableModels() {
    try {
      const url = `${this.baseUrl}/models`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`OpenRouter API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();
      
      return data.data?.map(model => ({
        id: model.id,
        name: model.name,
        description: model.description,
        pricing: model.pricing,
        context_length: model.context_length,
        architecture: model.architecture,
        top_provider: model.top_provider
      })) || [];
    } catch (error) {
      console.error('Chyba při získávání seznamu modelů z OpenRouter:', error);
      throw error;
    }
  }

  /**
   * Test připojení k OpenRouter
   * @returns {Promise<boolean>} True pokud je připojení úspěšné
   */
  async testConnection() {
    try {
      await this.getCompletion('Test připojení');
      return true;
    } catch (error) {
      console.error('Test připojení k OpenRouter selhal:', error);
      return false;
    }
  }

  /**
   * Získání informací o modelu
   * @returns {Object} Informace o modelu
   */
  getModelInfo() {
    return {
      provider: 'openrouter',
      model: this.model,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
      baseUrl: this.baseUrl
    };
  }

  /**
   * Získání populárních modelů pro OpenRouter
   * @returns {Array} Seznam populárních modelů
   */
  static getPopularModels() {
    return [
      'openai/gpt-4o-mini',
      'openai/gpt-4o',
      'anthropic/claude-3.5-sonnet',
      'anthropic/claude-3-haiku',
      'google/gemini-pro-1.5',
      'meta-llama/llama-3.1-8b-instruct',
      'mistralai/mistral-7b-instruct',
      'microsoft/wizardlm-2-8x22b'
    ];
  }

  /**
   * Získání cenových informací pro model
   * @param {string} modelId - ID modelu
   * @returns {Promise<Object>} Cenové informace
   */
  async getModelPricing(modelId = null) {
    try {
      const model = modelId || this.model;
      const models = await this.getAvailableModels();
      const modelInfo = models.find(m => m.id === model);
      
      return modelInfo?.pricing || null;
    } catch (error) {
      console.error('Chyba při získávání cenových informací:', error);
      return null;
    }
  }
}

module.exports = OpenRouterProvider;
