{"version": 3, "file": "service-worker.js", "sources": ["C:/Users/<USER>/AppData/Local/Temp/94988decca0c4897eeae83c1da07a0f1/service-worker.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {StaleWhileRevalidate as workbox_strategies_StaleWhileRevalidate} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-strategies/StaleWhileRevalidate.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'G:/Programovani/lazorikcodinglabs/node_modules/workbox-precaching/precacheAndRoute.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"chat.html\",\n    \"revision\": \"********************************\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"60b82a7b51a3bc4bdc02d58dc09396d3\"\n  },\n  {\n    \"url\": \"js/auth.31d6cfe0d16ae931b73c.min.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"js/auth0-bundle.43745bf1af764cfffebb.min.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"js/chat.31d6cfe0d16ae931b73c.min.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"js/main.b21208263df11332c6a7.min.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"js/supabase.31d6cfe0d16ae931b73c.min.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"js/sync.31d6cfe0d16ae931b73c.min.js\",\n    \"revision\": null\n  }\n], {});\n\n\n\n\nworkbox_routing_registerRoute(/\\.(?:png|jpg|jpeg|svg|gif)$/, new workbox_strategies_CacheFirst({ \"cacheName\":\"images\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 60, maxAgeSeconds: 2592000 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:js|css)$/, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-resources\", plugins: [] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/api\\.mapbox\\.com\\//, new workbox_strategies_CacheFirst({ \"cacheName\":\"mapbox-api\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 100, maxAgeSeconds: 604800 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_routing_registerRoute", "workbox_strategies_CacheFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds", "workbox_strategies_StaleWhileRevalidate"], "mappings": "0nBAwBAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,YACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,8CACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,0CACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,OAEb,CAAE,GAKLC,EAAAA,cAA8B,8BAA+B,IAAIC,aAA8B,CAAEC,UAAY,SAAUC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,YAAgB,OAC1NN,EAAAA,cAA8B,gBAAiB,IAAIO,uBAAwC,CAAEL,UAAY,mBAAoBC,QAAS,KAAO,OAC7IH,EAAAA,cAA8B,gCAAiC,IAAIC,aAA8B,CAAEC,UAAY,aAAcC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,IAAKC,cAAe,YAAe"}