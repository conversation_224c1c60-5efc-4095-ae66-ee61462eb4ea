/**
 * Styly pro reklamní modul
 * Verze 0.3.8.7
 */

/* Kontejner pro reklamy */
#ads-container {
    position: fixed;
    z-index: 800;
    pointer-events: none;
}

/* Reklamní sloty */
.ad-slot {
    position: fixed;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 10px;
    padding: 10px;
    display: none;
    pointer-events: auto;
    z-index: 810;
    transition: all 0.3s ease;
}

.ad-slot:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* <PERSON><PERSON>kos<PERSON> reklam */
.ad-small {
    width: 200px;
    height: 100px;
}

.ad-medium {
    width: 300px;
    height: 150px;
}

.ad-large {
    width: 400px;
    height: 250px;
}

/* <PERSON><PERSON><PERSON> reklam */
.ad-position-sidebar {
    top: 80px;
    right: 20px;
}

.ad-position-overlay {
    bottom: 20px;
    left: 20px;
}

.ad-position-search {
    top: 80px;
    left: 20px;
}

.ad-position-fullscreen {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* Štítek reklamy */
.ad-label {
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-bottom-left-radius: 5px;
}

/* Obsah reklamy */
.ad-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    cursor: pointer;
}

/* Obrázek reklamy */
.ad-image {
    max-width: 100%;
    max-height: 60%;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 5px;
}

/* Informace o reklamě */
.ad-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Nadpis reklamy */
.ad-title {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 5px 0;
    color: var(--text-color);
}

/* Popis reklamy */
.ad-description {
    font-size: 12px;
    margin: 0;
    color: var(--text-color-secondary);
}

/* Tlačítko pro zavření reklamy */
.ad-close {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 16px;
    line-height: 18px;
    text-align: center;
    cursor: pointer;
    border-bottom-right-radius: 5px;
}

.ad-close:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Zpráva o AdBlockeru */
.ad-blocker-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.ad-blocker-content {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 20px;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.ad-blocker-content h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.ad-blocker-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.ad-blocker-actions button {
    padding: 8px 15px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

#ad-blocker-subscribe {
    background-color: var(--primary-color);
    color: white;
}

#ad-blocker-subscribe:hover {
    background-color: var(--primary-color-dark);
}

#ad-blocker-close {
    background-color: var(--background-color-secondary);
    color: var(--text-color);
}

#ad-blocker-close:hover {
    background-color: var(--border-color);
}

/* Responzivní design */
@media (max-width: 768px) {
    .ad-medium, .ad-large {
        width: 250px;
        height: 125px;
    }
    
    .ad-position-sidebar {
        top: auto;
        bottom: 20px;
        right: 20px;
    }
    
    .ad-position-fullscreen {
        width: 90% !important;
        height: auto !important;
        max-height: 80vh;
    }
}

/* Tmavý režim */
.dark-mode .ad-slot {
    background-color: var(--dark-background-color);
    border-color: var(--dark-border-color);
}

.dark-mode .ad-title {
    color: var(--dark-text-color);
}

.dark-mode .ad-description {
    color: var(--dark-text-color-secondary);
}

.dark-mode .ad-blocker-content {
    background-color: var(--dark-background-color);
}

.dark-mode #ad-blocker-close {
    background-color: var(--dark-background-color-secondary);
    color: var(--dark-text-color);
}

.dark-mode #ad-blocker-close:hover {
    background-color: var(--dark-border-color);
}
