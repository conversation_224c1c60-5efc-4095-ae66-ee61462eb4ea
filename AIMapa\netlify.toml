[build]
  command = "npm run build:netlify"
  publish = "public/dist"

[build.environment]
  NODE_VERSION = "18.16.0"
  NPM_VERSION = "8"
  NODE_ENV = "production"

[dev]
  command = "npm run dev"
  port = 3000
  targetPort = 3000
  framework = "#custom"
  publish = "public"
  autoLaunch = true
  envFiles = [".env", ".env.local"]

[[redirects]]
  from = "/callback"
  to = "/callback.html"
  status = 200
  force = true

[[redirects]]
  from = "/login"
  to = "/login.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth/callback"
  to = "/callback.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth/login"
  to = "/login.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth/logout"
  to = "/logout.html"
  status = 200
  force = true

[[redirects]]
  from = "/"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/profile"
  to = "/profile.html"
  status = 200
  force = true

[[redirects]]
  from = "/user-profile"
  to = "/user-profile.html"
  status = 200
  force = true

[[redirects]]
  from = "/demo"
  to = "/demo.html"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self), interest-cohort=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    # Enhanced Security Policy with unsafe-eval explicitly positioned for necessary scripts
    # Added support for crypto wallet providers and script-src-elem directive
    Content-Security-Policy = "default-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; script-src 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; img-src 'self' data: https://*.auth0.com https://api.mapy.cz https://unpkg.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://cdn.auth0.com https://via.placeholder.com https://*.googleusercontent.com; connect-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://dev-zxj8pir0moo4pdk7.us.auth0.com https://*.stripe.com https://*.googleapis.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; frame-src 'self' https://*.auth0.com https://*.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; worker-src 'self' blob:; manifest-src 'self'; font-src 'self' data: https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content; script-src-elem 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com;"
