<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AIMapa - Interaktivní mapa s AI funkcemi">
    <meta name="keywords" content="mapa, AI, interaktivní, navigace, vyhledávání">
    <meta name="author" content="Jan Lazorík">
    <meta name="theme-color" content="#4285F4">
    
    <title>AIMapa - Interaktivní mapa s AI funkcemi</title>

    <!-- Favicon -->
    <link rel="icon" href="/assets/favicon.svg" type="image/svg+xml">
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">
    <link rel="manifest" href="/assets/manifest.json">

    <!-- Core styles -->
    <link rel="stylesheet" href="/app/styles/styles.css">
    <link rel="stylesheet" href="/app/services/auth/auth-service.css">
    <link rel="stylesheet" href="/app/services/auth/role-manager.css">
    <link rel="stylesheet" href="/app/components/admin/admin-dashboard.css">
    <link rel="stylesheet" href="/app/components/search/place-search.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
          integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            text-align: center;
        }
        .error-message button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
        }
    </style>

    <!-- External dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
          crossorigin=""></script>

    <!-- Leaflet dependencies -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Core scripts -->
    <script src="/app/core/config/env-config.js"></script>
    <script src="/app/services/auth/auth0-auth.js"></script>
    <script src="/app/services/auth/auth-screen.js"></script>
    <script src="/app/role-manager.js"></script>
    <script src="/app/admin-dashboard.js"></script>
    <script src="/app/leaflet-init.js"></script>
    <script src="/app/components/map/map.js"></script>
    <script src="/app/core/app.js"></script>

    <!-- Map providers -->
    <script src="/app/services/map/map-providers.js"></script>
    <script src="/app/services/map/mapycz-provider.js"></script>
    <script src="/app/services/map/openrouteservice-provider.js"></script>
    <script src="/app/services/map/windy-provider.js"></script>
    <script src="/app/services/map/freemapsk-provider.js"></script>
    <script src="/app/services/map/google-maps-provider.js"></script>
    <script src="/app/components/map/map-init-providers.js"></script>
    <script src="/app/components/map/map.js"></script>
    <script src="/app/components/settings/map-settings.js"></script>
    <script src="/app/components/search/place-search.js"></script>
    <script src="/app/components/chat/chat-component.js"></script>



    <!-- Inicializace komponent -->
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Inicializace Auth0
                console.log('Inicializace Auth0...');
                await Auth0Auth.init();

                // Inicializace přihlašovací obrazovky
                console.log('Inicializace přihlašovací obrazovky...');
                await AuthScreen.init();

                // Inicializace Role Manageru
                console.log('Inicializace Role Manageru...');
                if (typeof RoleManager !== 'undefined') {
                    await RoleManager.init();

                    // Inicializace Admin Dashboardu pouze pro adminy
                    if (RoleManager.hasRole('admin')) {
                        console.log('Inicializace Admin Dashboardu...');
                        await AdminDashboard.init();
                    }
                }

                console.log('Inicializace komponent dokončena');
            } catch (error) {
                console.error('Chyba při inicializaci komponent:', error);
            }
        });
    </script>

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    

    <!-- Browser compatibility polyfills -->
    <script src="/js/polyfills.js"></script>

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; script-src 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; img-src 'self' data: https://*.auth0.com https://api.mapy.cz https://unpkg.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://cdn.auth0.com https://via.placeholder.com https://*.googleusercontent.com; connect-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://dev-zxj8pir0moo4pdk7.us.auth0.com https://*.stripe.com https://*.googleapis.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; frame-src 'self' https://*.auth0.com https://*.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; worker-src 'self' blob:; manifest-src 'self'; font-src 'self' data: https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content; script-src-elem 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <a href="/">
                        <img src="/images/logo.svg" alt="AIMapa Logo" onerror="this.src='/favicon.ico'">
                        <span>AIMapa</span>
                    </a>
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="/" class="active">Domů</a></li>
                        <li><a href="/pages/map.html">Mapa</a></li>
                        <li><a href="/pages/api-keys.html">API klíče</a></li>
                        <li><a href="/pages/profile.html">Profil</a></li>
                    </ul>
                </nav>
                <div id="auth-status">
                    <span id="login-status">Kontrola přihlášení...</span>
                    <a href="/auth/login.html" id="login-btn" style="display:none;">Přihlásit se</a>
                    <a href="/pages/profile.html" id="profile-btn" style="display:none;">Profil</a>
                    <a href="/auth/logout.html" id="logout-btn" style="display:none;">Odhlásit se</a>
                </div>
            </div>
        </header>

        <!-- Main content -->
        <main id="main-content">
            <h1>AI Mapa</h1>
            <p>Vítejte v aplikaci AI Mapa!</p>

            <div class="main-container">
                <!-- Map container -->
                <div class="map-container">
                    <div id="map" style="width: 100%; height: 600px;"></div>
                    <div id="map-controls">
                        <div class="search-container">
                            <input type="text" id="place-search" placeholder="Vyhledat místo...">
                            <button id="search-button">Hledat</button>
                        </div>
                        <div class="map-provider-selector">
                            <select id="map-provider">
                                <option value="google">Google Maps</option>
                                <option value="mapycz">Mapy.cz</option>
                                <option value="openstreetmap">OpenStreetMap</option>
                                <option value="freemapsk">Freemap.sk</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Chat container -->
                <div class="chat-container">
                    <div class="chat-header">
                        <h2>AI Asistent</h2>
                        <div class="model-selector">
                            <select id="model-selector">
                                <option value="gemini-pro">Gemini Pro</option>
                                <option value="gpt-4">GPT-4</option>
                                <option value="claude">Claude</option>
                                <option value="deepseek">DeepSeek</option>
                            </select>
                        </div>
                    </div>
                    <div class="chat-messages" id="chat-messages">
                        <div class="message system">
                            <div class="message-content">
                                <p>Vítejte v AI Mapě! Jak vám mohu pomoci s navigací nebo vyhledáváním míst?</p>
                            </div>
                        </div>
                    </div>
                    <div class="chat-input">
                        <textarea id="user-input" placeholder="Napište zprávu..."></textarea>
                        <button id="send-button">Odeslat</button>
                    </div>
                </div>
            </div>
        </main>

        <style>
            .main-container {
                display: flex;
                flex-direction: row;
                gap: 20px;
                margin: 20px 0;
            }

            @media (max-width: 768px) {
                .main-container {
                    flex-direction: column;
                }
            }

            .map-container {
                flex: 3;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            #map-controls {
                padding: 10px;
                background-color: #f5f5f5;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            .search-container {
                display: flex;
                margin-bottom: 10px;
            }

            .search-container input {
                flex: 1;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px 0 0 4px;
            }

            .search-container button {
                padding: 8px 15px;
                background-color: #4285F4;
                color: white;
                border: none;
                border-radius: 0 4px 4px 0;
                cursor: pointer;
            }

            .map-provider-selector select {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }

            .chat-container {
                flex: 2;
                display: flex;
                flex-direction: column;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                background-color: #fff;
                height: 600px;
            }

            .chat-header {
                padding: 15px;
                background-color: #4285F4;
                color: white;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .chat-header h2 {
                margin: 0;
                font-size: 18px;
            }

            .model-selector select {
                padding: 5px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }

            .chat-messages {
                flex: 1;
                padding: 15px;
                overflow-y: auto;
                background-color: #f9f9f9;
            }

            .message {
                margin-bottom: 15px;
                max-width: 80%;
            }

            .message.user {
                margin-left: auto;
                background-color: #DCF8C6;
                border-radius: 10px 0 10px 10px;
                padding: 10px;
            }

            .message.system {
                margin-right: auto;
                background-color: #E5E5EA;
                border-radius: 0 10px 10px 10px;
                padding: 10px;
            }

            .message-content p {
                margin: 0;
            }

            .chat-input {
                display: flex;
                padding: 10px;
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
            }

            .chat-input textarea {
                flex: 1;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                resize: none;
                height: 40px;
            }

            .chat-input button {
                padding: 0 15px;
                margin-left: 10px;
                background-color: #4285F4;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
        </style>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>AIMapa</h3>
                    <p>Interaktivní mapa s AI funkcemi</p>
                </div>
                <div class="footer-section">
                    <h3>Odkazy</h3>
                    <ul>
                        <li><a href="/">Domů</a></li>
                        <li><a href="/pages/map.html">Mapa</a></li>
                        <li><a href="/pages/api-keys.html">API klíče</a></li>
                        <li><a href="/pages/profile.html">Profil</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Kontakt</h3>
                    <p>Email: <EMAIL></p>
                    <p>Telefon: +420 123 456 789</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AIMapa. Všechna práva vyhrazena.</p>
            </div>
        </footer>
    </div>

    <script>
        // Kontrola stavu přihlášení
        document.addEventListener('DOMContentLoaded', function() {
            fetch('/auth/status')
                .then(response => response.json())
                .then(data => {
                    const loginStatus = document.getElementById('login-status');
                    const loginBtn = document.getElementById('login-btn');
                    const profileBtn = document.getElementById('profile-btn');
                    const logoutBtn = document.getElementById('logout-btn');

                    if (data.isAuthenticated) {
                        loginStatus.textContent = `Přihlášen jako: ${data.user.auth0.name || data.user.auth0.email || 'Uživatel'}`;
                        loginBtn.style.display = 'none';
                        profileBtn.style.display = 'inline-block';
                        logoutBtn.style.display = 'inline-block';
                    } else {
                        loginStatus.textContent = 'Nepřihlášen';
                        loginBtn.style.display = 'inline-block';
                        profileBtn.style.display = 'none';
                        logoutBtn.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Chyba při kontrole stavu přihlášení:', error);
                    document.getElementById('login-status').textContent = 'Chyba při kontrole přihlášení';
                });

            // Inicializace mapy
            if (typeof MapModule !== 'undefined' && document.getElementById('map')) {
                try {
                    MapModule.init();
                } catch (error) {
                    console.error('Chyba při inicializaci mapy:', error);
                    const mapElement = document.getElementById('map');
                    if (mapElement) {
                        mapElement.innerHTML = '<div class="error-message">Chyba při načítání mapy. <button onclick="location.reload()">Zkusit znovu</button></div>';
                    }
                }
            } else {
                // Fallback inicializace mapy
                initMap();
            }

            // Inicializace chatu
            initChat();
        });

        // Jednoduchá inicializace mapy
        function initMap() {
            try {
                const mapElement = document.getElementById('map');
                if (!mapElement) return;

                // Inicializace mapy pomocí Leaflet
                const map = L.map('map').setView([50.0755, 14.4378], 13); // Praha

                // Přidání OpenStreetMap vrstvy
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                // Přidání markeru
                L.marker([50.0755, 14.4378]).addTo(map)
                    .bindPopup('Praha')
                    .openPopup();

                // Změna poskytovatele mapy
                const mapProviderSelect = document.getElementById('map-provider');
                if (mapProviderSelect) {
                    mapProviderSelect.addEventListener('change', function() {
                        const provider = this.value;
                        let tileLayer;

                        // Odstranění stávající vrstvy
                        map.eachLayer(layer => {
                            if (layer instanceof L.TileLayer) {
                                map.removeLayer(layer);
                            }
                        });

                        // Přidání nové vrstvy podle vybraného poskytovatele
                        switch (provider) {
                            case 'mapycz':
                                tileLayer = L.tileLayer('https://mapserver.mapy.cz/turist-m/{z}-{x}-{y}', {
                                    attribution: '&copy; <a href="https://www.seznam.cz">Seznam.cz, a.s.</a>'
                                });
                                break;
                            case 'google':
                                tileLayer = L.tileLayer('https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
                                    attribution: '&copy; Google Maps'
                                });
                                break;
                            case 'freemapsk':
                                tileLayer = L.tileLayer('https://outdoor.tiles.freemap.sk/{z}/{x}/{y}', {
                                    attribution: '&copy; <a href="https://www.freemap.sk">Freemap Slovakia</a>'
                                });
                                break;
                            default: // openstreetmap
                                tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                });
                        }

                        tileLayer.addTo(map);
                    });
                }

                // Vyhledávání míst
                const searchButton = document.getElementById('search-button');
                const searchInput = document.getElementById('place-search');

                if (searchButton && searchInput) {
                    searchButton.addEventListener('click', function() {
                        const query = searchInput.value.trim();
                        if (!query) return;

                        // Přidání zprávy do chatu
                        addMessage('user', query);

                        // Simulace vyhledávání
                        setTimeout(() => {
                            addMessage('system', `Vyhledávám místo: "${query}"...`);

                            // Simulace nalezení místa
                            setTimeout(() => {
                                const randomLat = 50.0755 + (Math.random() - 0.5) * 0.1;
                                const randomLng = 14.4378 + (Math.random() - 0.5) * 0.1;

                                // Přidání markeru
                                L.marker([randomLat, randomLng]).addTo(map)
                                    .bindPopup(query)
                                    .openPopup();

                                // Přesun mapy na nalezené místo
                                map.setView([randomLat, randomLng], 14);

                                // Přidání odpovědi do chatu
                                addMessage('system', `Nalezeno místo "${query}" na souřadnicích [${randomLat.toFixed(4)}, ${randomLng.toFixed(4)}].`);
                            }, 1000);
                        }, 500);
                    });

                    // Přidání posluchače události pro stisknutí klávesy Enter
                    searchInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            searchButton.click();
                        }
                    });
                }
            } catch (error) {
                console.error('Chyba při inicializaci mapy:', error);
                const mapElement = document.getElementById('map');
                if (mapElement) {
                    mapElement.innerHTML = '<div class="error-message">Chyba při načítání mapy. <button onclick="location.reload()">Zkusit znovu</button></div>';
                }
            }
        }

        // Inicializace chatu
        function initChat() {
            const chatMessages = document.getElementById('chat-messages');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const modelSelector = document.getElementById('model-selector');

            if (!chatMessages || !userInput || !sendButton) return;

            // Přidání posluchače události pro tlačítko odeslání
            sendButton.addEventListener('click', function() {
                const message = userInput.value.trim();
                if (!message) return;

                // Přidání zprávy uživatele do chatu
                addMessage('user', message);

                // Vyčištění vstupu
                userInput.value = '';

                // Simulace odpovědi AI
                const selectedModel = modelSelector ? modelSelector.value : 'gemini-pro';
                simulateAIResponse(message, selectedModel);
            });

            // Přidání posluchače události pro stisknutí klávesy Enter
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendButton.click();
                }
            });
        }

        // Přidání zprávy do chatu
        function addMessage(type, content) {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const paragraph = document.createElement('p');
            paragraph.textContent = content;

            contentDiv.appendChild(paragraph);
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);

            // Scrollování na konec chatu
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Simulace odpovědi AI
        function simulateAIResponse(userMessage, model) {
            // Zobrazení indikátoru psaní
            addMessage('system', '...');

            // Simulace prodlevy
            setTimeout(() => {
                // Odstranění indikátoru psaní
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages && chatMessages.lastChild) {
                    chatMessages.removeChild(chatMessages.lastChild);
                }

                // Generování odpovědi podle modelu
                let response;
                switch (model) {
                    case 'gpt-4':
                        response = `GPT-4: Zpracovávám váš dotaz "${userMessage}". Mohu vám pomoci s vyhledáváním míst, plánováním tras nebo poskytnutím informací o zajímavých místech.`;
                        break;
                    case 'claude':
                        response = `Claude: Děkuji za váš dotaz "${userMessage}". Jsem zde, abych vám pomohl s navigací, vyhledáváním míst nebo poskytnutím užitečných informací o lokalitách.`;
                        break;
                    case 'deepseek':
                        response = `DeepSeek: Analyzuji váš požadavek "${userMessage}". Mohu vám pomoci najít místa, naplánovat trasu nebo poskytnout další informace o zajímavých lokalitách.`;
                        break;
                    default: // gemini-pro
                        response = `Gemini Pro: Rozumím vašemu dotazu "${userMessage}". Mohu vám pomoci s vyhledáváním míst, plánováním tras nebo poskytnutím informací o zajímavých lokalitách v okolí.`;
                }

                // Přidání odpovědi do chatu
                addMessage('system', response);
            }, 1500);
        }
    </script>
</body>
</html>
