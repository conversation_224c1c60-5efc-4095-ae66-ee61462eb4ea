<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMapa</title>

    <!-- Core styles -->
    <link rel="stylesheet" href="/app/styles/styles.css">
    <link rel="stylesheet" href="/app/services/auth/auth-service.css">
    <link rel="stylesheet" href="/app/role-manager.css">
    <link rel="stylesheet" href="/app/admin-dashboard.css">
    <link rel="stylesheet" href="/app/monetization.css">
    <link rel="stylesheet" href="/app/advertisement-module.css">

    <!-- External dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>

    <!-- Leaflet dependencies -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Core scripts -->
    <script src="/env-config.js"></script>
    <script src="/app/services/auth/auth0-auth.js"></script>
    <script src="/app/services/auth/auth-screen.js"></script>
    <script src="/app/role-manager.js"></script>
    <script src="/app/admin-dashboard.js"></script>
    <script src="/app/leaflet-init.js"></script>
    <script src="/app/components/map/map.js"></script>
    <script src="/app/core/app.js"></script>

    <!-- Map providers -->
    <script src="/app/services/map/map-providers.js"></script>
    <script src="/app/services/map/mapycz-provider.js"></script>
    <script src="/app/services/map/openrouteservice-provider.js"></script>
    <script src="/app/services/map/windy-provider.js"></script>
    <script src="/app/services/map/freemapsk-provider.js"></script>
    <script src="/app/components/map/map-init-providers.js"></script>

    <!-- Monetization -->
    <script src="/app/advertisement-module.js"></script>

    <!-- Virtual Work -->
    <script src="/app/modules/virtual-work/virtual-work.js"></script>

    <!-- Inicializace komponent -->
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Inicializace Auth0
                console.log('Inicializace Auth0...');
                await Auth0Auth.init();

                // Inicializace přihlašovací obrazovky
                console.log('Inicializace přihlašovací obrazovky...');
                await AuthScreen.init();

                // Inicializace Role Manageru
                console.log('Inicializace Role Manageru...');
                if (typeof RoleManager !== 'undefined') {
                    await RoleManager.init();

                    // Inicializace Admin Dashboardu pouze pro adminy
                    if (RoleManager.hasRole('admin')) {
                        console.log('Inicializace Admin Dashboardu...');
                        await AdminDashboard.init();
                    }
                }

                console.log('Inicializace komponent dokončena');
            } catch (error) {
                console.error('Chyba při inicializaci komponent:', error);
            }
        });
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" 
      integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" 
      crossorigin="anonymous" referrerpolicy="no-referrer" />

    

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    

    <!-- Browser compatibility polyfills -->
    <script src="/js/polyfills.js"></script>

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; script-src 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; img-src 'self' data: https://*.auth0.com https://api.mapy.cz https://unpkg.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://cdn.auth0.com https://via.placeholder.com https://*.googleusercontent.com; connect-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://dev-zxj8pir0moo4pdk7.us.auth0.com https://*.stripe.com https://*.googleapis.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; frame-src 'self' https://*.auth0.com https://*.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; worker-src 'self' blob:; manifest-src 'self'; font-src 'self' data: https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content; script-src-elem 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com">
</head>
<body>
    <div id="app">
        <!-- User menu container -->
        <div id="user-menu">
            <div id="auth-status">
                <span id="login-status">Kontrola přihlášení...</span>
                <a href="/login" id="login-btn" style="display:none;">Přihlásit se</a>
                <a href="/profile" id="profile-btn" style="display:none;">Profil</a>
                <a href="/auth/logout" id="logout-btn" style="display:none;">Odhlásit se</a>
            </div>
        </div>

        <!-- Main content -->
        <main id="main-content">
            <!-- Content will be dynamically inserted here -->
            <h1>AI Mapa</h1>
            <p>Vítejte v aplikaci AI Mapa!</p>

            <!-- Map container -->
            <div id="map" style="width: 100%; height: 500px; margin-top: 20px;"></div>
        </main>
    </div>

    <script>
        // Kontrola stavu přihlášení
        document.addEventListener('DOMContentLoaded', function() {
            const loginStatus = document.getElementById('login-status');
            const loginBtn = document.getElementById('login-btn');
            const profileBtn = document.getElementById('profile-btn');
            const logoutBtn = document.getElementById('logout-btn');

            // Kontrola, zda je uživatel přihlášen pomocí localStorage
            const isLoggedIn = localStorage.getItem('aiMapaLoggedIn') === 'true';

            if (isLoggedIn) {
                loginStatus.textContent = 'Přihlášen';
                loginBtn.style.display = 'none';
                profileBtn.style.display = 'inline-block';
                logoutBtn.style.display = 'inline-block';
            } else {
                loginStatus.textContent = 'Nepřihlášen';
                loginBtn.style.display = 'inline-block';
                profileBtn.style.display = 'none';
                logoutBtn.style.display = 'none';
            }

            // Přidání posluchače události pro odhlášení
            logoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('aiMapaLoggedIn');
                window.location.reload();
            });
        });
    </script>
</body>
</html>
