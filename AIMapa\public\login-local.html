<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMapa - Př<PERSON>í</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 5px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .dev-note {
            margin-top: 2rem;
            padding: 1rem;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            font-size: 0.9rem;
            color: #856404;
        }

        .quick-login {
            margin-top: 1rem;
            text-align: center;
        }

        .quick-login button {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🗺️ AIMapa</h1>
            <p>Lokální přihlášení pro vývoj</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Uživatelské jméno:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Heslo:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn" id="loginBtn">Přihlásit se</button>
        </form>

        <div class="quick-login">
            <p>Rychlé přihlášení:</p>
            <button onclick="quickLogin()">Demo uživatel</button>
        </div>

        <div id="message"></div>

        <div class="dev-note">
            <strong>Vývojářská poznámka:</strong><br>
            Toto je zjednodušená autentizace pro lokální vývoj. 
            Jakékoliv uživatelské jméno a heslo bude akceptováno.
        </div>
    </div>

    <script>
        const loginForm = document.getElementById('loginForm');
        const messageDiv = document.getElementById('message');
        const loginBtn = document.getElementById('loginBtn');

        function showMessage(text, type = 'info') {
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
        }

        function quickLogin() {
            document.getElementById('username').value = 'demo';
            document.getElementById('password').value = 'demo';
            loginForm.dispatchEvent(new Event('submit'));
        }

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('Vyplňte všechna pole', 'error');
                return;
            }

            loginBtn.disabled = true;
            loginBtn.textContent = 'Přihlašuji...';

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('Úspěšně přihlášen! Přesměrovávám...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showMessage(data.error || 'Chyba při přihlašování', 'error');
                }
            } catch (error) {
                console.error('Chyba při přihlašování:', error);
                showMessage('Chyba při komunikaci se serverem', 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Přihlásit se';
            }
        });

        // Kontrola, zda už není uživatel přihlášen
        fetch('/auth/status')
            .then(response => response.json())
            .then(data => {
                if (data.isAuthenticated) {
                    showMessage('Už jste přihlášen! Přesměrovávám...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                }
            })
            .catch(error => {
                console.error('Chyba při kontrole stavu přihlášení:', error);
            });
    </script>
</body>
</html>
