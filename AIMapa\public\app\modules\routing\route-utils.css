/**
 * <PERSON>yly pro nástroje sou<PERSON><PERSON><PERSON><PERSON><PERSON> s trasou
 * Verze 0.2.8.6.4
 */

/* <PERSON>dik<PERSON><PERSON> trasy */
.route-loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    z-index: 2000;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: pulse 1.5s infinite ease-in-out;
}

.route-loading-indicator .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
    100% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
}

/* Tlačítko pro zobrazení celé trasy */
.fit-route-button {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: background-color 0.3s, transform 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.fit-route-button::before {
    content: '📶'; /* Ikona antény */
    font-size: 16px;
}

.fit-route-button:hover {
    background-color: #388E3C;
    transform: scale(1.05);
}

.fit-route-button:active {
    transform: scale(0.98);
}

/* Animace pro tlačítko */
.fit-route-button.new {
    animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Tmavý režim */
body[data-theme="dark"] .route-loading-indicator {
    background-color: rgba(51, 51, 51, 0.9);
}

body[data-theme="dark"] .fit-route-button {
    background-color: #388E3C;
}

body[data-theme="dark"] .fit-route-button:hover {
    background-color: #2E7D32;
}

/* Responzivní design */
@media (max-width: 768px) {
    .fit-route-button {
        bottom: 70px;
        right: 10px;
        padding: 8px 12px;
        font-size: 14px;
    }
}
