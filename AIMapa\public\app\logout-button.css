/**
 * Styly pro odhlašovací tlačítko v AIMapa
 * Verze 0.3.8.5
 */

/* Tlačítko pro odhlášení */
.logout-button {
    position: fixed;
    top: 20px;
    right: 180px; /* Umístění vedle přihlašovacího tlačítka */
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: #e74c3c; /* Červená barva */
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Zv<PERSON>šen<PERSON> z-indexu, aby bylo tlačítko vždy viditelné */
    transition: all 0.3s ease;
    font-size: 18px;
    overflow: hidden;
    animation: pulse-red 2s infinite; /* <PERSON>řid<PERSON><PERSON> pulzují<PERSON> animace pro lepší viditelnost */
}

.logout-button:hover {
    transform: scale(1.1) translateY(-2px);
    background-color: #c0392b; /* Tmavší červená barva */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.logout-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.logout-button:hover::before {
    left: 100%;
}

/* Tooltip pro odhlašovací tlačítko */
.logout-button::after {
    content: 'Odhlásit se';
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.logout-button:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Animace pro odhlašovací tlačítko */
@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

/* Notifikace o odhlašování */
.logout-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.logout-notification-content {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logout-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
