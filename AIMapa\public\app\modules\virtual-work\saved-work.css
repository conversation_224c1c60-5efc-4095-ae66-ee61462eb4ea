/**
 * Styly pro nedokončenou práci
 * Verze 0.3.3.0
 */

/* Banner s nedokončenou prací */
.saved-work-banner {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    animation: pulse-light 2s infinite;
}

.saved-work-banner-icon {
    font-size: 24px;
    margin-right: 15px;
}

.saved-work-banner-text {
    flex: 1;
    font-weight: 600;
}

.saved-work-banner-btn {
    background-color: white;
    color: #2980b9;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.saved-work-banner-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

@keyframes pulse-light {
    0% {
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 3px 15px rgba(52, 152, 219, 0.3);
    }
    100% {
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }
}

/* Notifikace o uložené práci */
.saved-work-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    overflow: hidden;
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.saved-work-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.saved-work-notification-content {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.saved-work-notification-icon {
    font-size: 24px;
    margin-right: 15px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
}

.saved-work-notification-text {
    flex: 1;
}

.saved-work-notification-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.saved-work-notification-desc {
    font-size: 14px;
    color: #7f8c8d;
}

.saved-work-notification-close {
    background: none;
    border: none;
    color: #95a5a6;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.saved-work-notification-close:hover {
    color: #e74c3c;
}

.saved-work-notification-actions {
    padding: 10px 15px;
    display: flex;
    justify-content: flex-end;
}

.saved-work-notification-resume {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.saved-work-notification-resume:hover {
    background-color: #2980b9;
}

/* Seznam uložených prací */
.saved-work-container {
    padding: 20px;
}

.saved-work-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 20px;
    text-align: center;
}

.saved-work-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.saved-work-item {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.saved-work-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.saved-work-icon {
    font-size: 24px;
    margin-right: 15px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
}

.saved-work-info {
    flex: 1;
}

.saved-work-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.saved-work-date {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.saved-work-details {
    display: flex;
    gap: 15px;
}

.saved-work-time, .saved-work-tasks {
    font-size: 13px;
    color: #7f8c8d;
}

.saved-work-resume, .saved-work-delete {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.saved-work-resume {
    color: #27ae60;
    margin-right: 5px;
}

.saved-work-resume:hover {
    background-color: rgba(39, 174, 96, 0.1);
    transform: scale(1.1);
}

.saved-work-delete {
    color: #e74c3c;
}

.saved-work-delete:hover {
    background-color: rgba(231, 76, 60, 0.1);
    transform: scale(1.1);
}

.no-saved-work {
    text-align: center;
    padding: 30px;
    color: #95a5a6;
    font-style: italic;
}

/* Tmavý režim */
body[data-theme="dark"] .saved-work-notification {
    background-color: #2c3e50;
}

body[data-theme="dark"] .saved-work-notification-icon {
    background-color: #34495e;
}

body[data-theme="dark"] .saved-work-notification-title {
    color: #ecf0f1;
}

body[data-theme="dark"] .saved-work-notification-desc {
    color: #bdc3c7;
}

body[data-theme="dark"] .saved-work-notification-close {
    color: #bdc3c7;
}

body[data-theme="dark"] .saved-work-notification-close:hover {
    color: #e74c3c;
}

body[data-theme="dark"] .saved-work-notification-resume {
    background-color: #2980b9;
}

body[data-theme="dark"] .saved-work-notification-resume:hover {
    background-color: #3498db;
}

body[data-theme="dark"] .saved-work-container h3 {
    color: #ecf0f1;
}

body[data-theme="dark"] .saved-work-item {
    background-color: #34495e;
}

body[data-theme="dark"] .saved-work-icon {
    background-color: #2c3e50;
}

body[data-theme="dark"] .saved-work-name {
    color: #ecf0f1;
}

body[data-theme="dark"] .saved-work-date,
body[data-theme="dark"] .saved-work-time,
body[data-theme="dark"] .saved-work-tasks {
    color: #bdc3c7;
}

body[data-theme="dark"] .no-saved-work {
    color: #7f8c8d;
}

/* Animace pro přidání nového úkolu během práce */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.add-task-toggle.expanded {
    animation: pulse 1.5s infinite;
}

/* Styly pro drag-over indikátory */
.custom-task-item.drag-over-top {
    border-top: 2px solid #3498db;
}

.custom-task-item.drag-over-bottom {
    border-bottom: 2px solid #3498db;
}

.custom-task-item.dragging {
    opacity: 0.7;
    cursor: grabbing;
    z-index: 100;
    transform: scale(1.02) translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    background-color: #e8f4fc;
}
