<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <title>AIMapa - Mapa</title>
    <link rel="stylesheet" href="/app/styles/styles.css" />
    <link rel="stylesheet" href="/app/components/search/place-search.css" />
    <link rel="stylesheet" href="/app/components/chat/chat-component.css" />
    <link rel="stylesheet" href="/app/components/map/tools/distance-measurement.css" />

    <!-- Favicon -->
    <link rel="icon" href="/assets/favicon.svg" type="image/svg+xml">
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">
    <link rel="manifest" href="/assets/manifest.json">

    <!-- Leaflet dependencies -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
          crossorigin=""></script>

    <!-- Marked.js pro Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <style>
        body, html {
            margin: 0; padding: 0; height: 100%; width: 100%; font-family: Arial, sans-serif;
            display: flex; flex-direction: column;
        }
        #menu {
            background: #333; color: white; padding: 10px; display: flex; align-items: center;
            flex-wrap: wrap;
        }
        #menu button {
            margin-right: 10px; padding: 8px 12px; background: #555; border: none; color: white; cursor: pointer;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        #menu button.active {
            background: #4a90e2;
        }
        #mapContainer {
            flex: 1; position: relative; overflow: hidden;
        }
        #map {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            text-align: center;
        }
        .error-message button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
        }
    </style>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" 
      integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" 
      crossorigin="anonymous" referrerpolicy="no-referrer" />


    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    

    <!-- Browser compatibility polyfills -->
    <script src="/js/polyfills.js"></script>

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; script-src 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; img-src 'self' data: https://*.auth0.com https://api.mapy.cz https://unpkg.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://cdn.auth0.com https://via.placeholder.com https://*.googleusercontent.com; connect-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://dev-zxj8pir0moo4pdk7.us.auth0.com https://*.stripe.com https://*.googleapis.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; frame-src 'self' https://*.auth0.com https://*.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; worker-src 'self' blob:; manifest-src 'self'; font-src 'self' data: https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content; script-src-elem 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com">
</head>
<body>
    <div id="menu">
        <button id="btnMap" class="active">Mapa</button>
        <button id="btnAddPoint">Přidat bod</button>
        <button id="btnCreateRoute">Vytvořit trasu</button>
        <button id="btnSearch">Hledat místo</button>
        <button id="btnFullscreen">Fullscreen</button>
        <a href="/" style="margin-left: 10px; color: white; text-decoration: none; background: #555; padding: 8px 12px; border-radius: 4px; margin-bottom: 5px;">Domů</a>
        <a href="/pages/api-keys.html" style="margin-left: 10px; color: white; text-decoration: none; background: #4a90e2; padding: 8px 12px; border-radius: 4px; margin-bottom: 5px;">API klíče</a>
        <input type="text" id="searchInput" placeholder="Zadejte hledaný výraz..." style="margin-left: 10px; padding: 8px; border-radius: 4px; border: 1px solid #ccc; width: 200px; margin-bottom: 5px;" />
    </div>
    <div id="container">
        <div id="mapContainer">
            <div id="map"></div>
        </div>
    </div>

    <!-- Map scripts -->
    <script src="/app/services/map/map-providers.js"></script>
    <script src="/app/services/map/mapycz-provider.js"></script>
    <script src="/app/services/map/openrouteservice-provider.js"></script>
    <script src="/app/services/map/windy-provider.js"></script>
    <script src="/app/services/map/freemapsk-provider.js"></script>
    <script src="/app/services/map/google-maps-provider.js"></script>
    <script src="/app/components/map/map-init-providers.js"></script>
    <script src="/app/components/map/map.js"></script>
    <script src="/app/components/settings/map-settings.js"></script>
    <script src="/app/components/search/place-search.js"></script>
    <script src="/app/components/chat/chat-component.js"></script>
    <script src="/app/components/map/tools/distance-measurement.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializace mapy
            let mapInstance = null;
            if (typeof MapModule !== 'undefined' && document.getElementById('map')) {
                try {
                    mapInstance = MapModule.init();
                } catch (error) {
                    console.error('Chyba při inicializaci mapy:', error);
                    const mapElement = document.getElementById('map');
                    if (mapElement) {
                        mapElement.innerHTML = '<div class="error-message">Chyba při načítání mapy. <button onclick="location.reload()">Zkusit znovu</button></div>';
                    }
                }
            }

            // Inicializace chat komponenty
            if (typeof ChatComponent !== 'undefined' && mapInstance) {
                try {
                    ChatComponent.init(mapInstance);
                } catch (error) {
                    console.error('Chyba při inicializaci chat komponenty:', error);
                }
            }

            // Inicializace vyhledávání míst
            if (typeof PlaceSearch !== 'undefined' && mapInstance) {
                try {
                    PlaceSearch.init(mapInstance.map);
                } catch (error) {
                    console.error('Chyba při inicializaci vyhledávání míst:', error);
                }
            }

            // Tlačítko pro fullscreen
            document.getElementById('btnFullscreen').addEventListener('click', () => {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    }
                }
            });

            // Tlačítka pro další funkce
            document.getElementById('btnAddPoint').addEventListener('click', () => {
                alert('Funkce přidání bodu bude implementována v další verzi.');
            });

            document.getElementById('btnCreateRoute').addEventListener('click', () => {
                alert('Funkce vytvoření trasy bude implementována v další verzi.');
            });

            document.getElementById('btnSearch').addEventListener('click', () => {
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    if (typeof PlaceSearch !== 'undefined') {
                        PlaceSearch.handleSearch();
                    } else {
                        alert('Komponenta pro vyhledávání není k dispozici.');
                    }
                } else {
                    alert('Zadejte hledaný výraz.');
                }
            });

            // Vyhledávání po stisknutí Enter
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', (event) => {
                    if (event.key === 'Enter') {
                        if (typeof PlaceSearch !== 'undefined') {
                            PlaceSearch.handleSearch();
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
