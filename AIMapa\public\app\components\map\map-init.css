/* Updated styles for AIMapa with chat panel and improved buttons */

body, html {
    margin: 0; padding: 0; height: 100%; width: 100%; font-family: Arial, sans-serif;
    display: flex; flex-direction: column;
}

#menu {
    background: #333; color: white; padding: 10px; display: flex; align-items: center;
    flex-wrap: wrap;
}

#menu button {
    margin-right: 10px; padding: 8px 12px; background: #555; border: none; color: white; cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

#menu button:hover {
    background: #7c3aed;
}

#menu button.active {
    background: #8B5CF6;
}

#container {
    flex: 1; display: flex; height: calc(100% - 50px);
    overflow: hidden;
}

#mapContainer {
    flex: 3; position: relative; overflow: hidden;
}

#map, #simpleGlobeContainer {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
}

#simpleGlobeContainer {
    display: none;
    z-index: 1000;
}

#chatPanel {
    flex: 1; background: #1e1e2f; color: white; display: flex; flex-direction: column;
    border-left: 1px solid #444;
    font-size: 14px;
}

#chatHeader {
    padding: 10px; background: #2a2a3d; font-weight: bold; border-bottom: 1px solid #444;
}

#chatMessages {
    flex: 1; padding: 10px; overflow-y: auto;
}

.chat-message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.user {
    background: #8B5CF6;
    align-self: flex-end;
}

.chat-message.bot {
    background: #44475a;
    align-self: flex-start;
}

#chatInputContainer {
    display: flex; padding: 10px; border-top: 1px solid #444;
}

#chatInput {
    flex: 1; padding: 8px 12px; border-radius: 20px; border: none; outline: none;
    font-size: 14px;
}

#chatSendBtn {
    margin-left: 10px; padding: 8px 16px; background: #8B5CF6; border: none; border-radius: 20px;
    color: white; cursor: pointer;
    transition: background-color 0.3s ease;
}

#chatSendBtn:hover {
    background: #7c3aed;
}
