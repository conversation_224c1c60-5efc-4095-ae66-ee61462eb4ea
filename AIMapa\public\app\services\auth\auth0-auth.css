/**
 * <PERSON><PERSON>y pro Auth0 autentizaci v AIMapa
 * Verze 0.3.8.5
 */

/* Tlačítko pro autentizaci */
.auth0-auth-button {
    position: fixed;
    top: 20px;
    right: 120px; /* <PERSON>íst<PERSON><PERSON><PERSON> vedle ostatní<PERSON> */
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: #ff4f1f; /* Auth0 oranžová barva */
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
    transition: all 0.3s ease;
    font-size: 18px;
    overflow: hidden;
}

.auth0-auth-button:hover {
    transform: scale(1.1) translateY(-2px);
    background-color: #eb5424; /* Tmavší Auth0 oran<PERSON> barva */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.auth0-auth-button.logged-in {
    background-color: #44c7f4; /* Auth0 modrá barva */
    /* Přidání animace pulzování pro zvýraznění přihlášeného stavu */
    animation: pulse-blue 3s infinite;
    border: 2px solid rgba(255, 255, 255, 0.6);
}

.auth0-auth-button.logged-in:hover {
    background-color: #0d96c6; /* Tmavší Auth0 modrá barva */
    animation: none;
    transform: scale(1.1) translateY(-2px);
}

/* Přidání podpory pro profilový obrázek v Auth0 tlačítku */
.auth0-auth-button img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Animace pulzování pro zvýraznění přihlášeného stavu */
@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(68, 199, 244, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(68, 199, 244, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(68, 199, 244, 0);
    }
}

/* Styly pro Auth0 přihlašovací tlačítko v přihlašovacím formuláři */
.auth0-login-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background-color: #ff4f1f; /* Auth0 oranžová barva */
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    margin-top: 10px;
}

.auth0-login-button:hover {
    background-color: #eb5424; /* Tmavší Auth0 oranžová barva */
}

/* Tlačítko pro přihlášení v hlavičce */
.header-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.login-button {
    background-color: #ff4f1f; /* Auth0 oranžová barva */
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.login-button:hover {
    background-color: #eb5424; /* Tmavší Auth0 oranžová barva */
}

.login-button.logged-in {
    background-color: #44c7f4; /* Auth0 modrá barva */
}

.login-button.logged-in:hover {
    background-color: #16214d; /* Auth0 tmavě modrá barva */
}

/* Tlačítko pro přihlášení přes Auth0 v přihlašovacím formuláři */
.auth0-login-button {
    width: 100%;
    padding: 12px;
    margin-top: 10px;
    background-color: #ff4f1f; /* Auth0 oranžová barva */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    transition: background-color 0.2s;
}

.auth0-login-button:hover {
    background-color: #eb5424; /* Tmavší Auth0 oranžová barva */
}

.auth0-login-button i {
    font-size: 18px;
}

/* Oddělovač pro sociální přihlášení */
.auth-social-separator {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 20px 0;
    color: var(--text-color, #333);
}

.auth-social-separator::before,
.auth-social-separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color, #ddd);
}

.auth-social-separator::before {
    margin-right: 10px;
}

.auth-social-separator::after {
    margin-left: 10px;
}

/* Kontejner pro sociální přihlášení */
.auth-social-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}
