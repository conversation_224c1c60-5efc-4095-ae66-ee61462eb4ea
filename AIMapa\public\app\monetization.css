/**
 * Styly pro monetizaci
 * Verze 0.3.8.7
 */

/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
.premium-feature.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    position: relative;
}

.premium-feature.disabled::after {
    content: '🔒';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    color: var(--primary-color);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* Dialog s prémiovými mapami */
.premium-maps-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.premium-maps-content {
    background-color: var(--background-color);
    border-radius: 10px;
    padding: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.premium-maps-content h2 {
    color: var(--primary-color);
    text-align: center;
    margin-top: 0;
}

.premium-maps-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.premium-map-option {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.premium-map-option h3 {
    margin-top: 0;
    color: var(--text-color);
}

.premium-map-option p {
    flex: 1;
    margin-bottom: 15px;
    color: var(--text-color-secondary);
}

.buy-map-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.buy-map-button:hover {
    background-color: var(--primary-color-dark);
}

.premium-maps-subscription {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

#show-subscription-button {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

#show-subscription-button:hover {
    background-color: var(--secondary-color-dark);
}

.close-dialog-button {
    background-color: var(--background-color-secondary);
    color: var(--text-color);
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
    display: block;
    margin: 20px auto 0;
}

.close-dialog-button:hover {
    background-color: var(--border-color);
}

/* Zobrazení zůstatku */
.user-balance {
    position: fixed;
    top: 20px;
    right: 80px;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 5px 10px;
    font-size: 0.9rem;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 900;
}

.user-balance-icon {
    color: var(--primary-color);
    margin-right: 5px;
}

/* Tlačítko pro prémiové mapy */
#btnPremiumMaps {
    background-color: var(--primary-color);
}

#btnPremiumMaps:hover {
    background-color: var(--primary-color-dark);
}

/* Responzivní design */
@media (max-width: 768px) {
    .premium-maps-options {
        grid-template-columns: 1fr;
    }
    
    .user-balance {
        top: auto;
        bottom: 20px;
        right: 20px;
    }
}

/* Tmavý režim */
.dark-mode .premium-maps-content {
    background-color: var(--dark-background-color);
}

.dark-mode .premium-map-option {
    border-color: var(--dark-border-color);
}

.dark-mode .premium-map-option h3 {
    color: var(--dark-text-color);
}

.dark-mode .premium-map-option p {
    color: var(--dark-text-color-secondary);
}

.dark-mode .premium-maps-subscription {
    border-top-color: var(--dark-border-color);
}

.dark-mode .close-dialog-button {
    background-color: var(--dark-background-color-secondary);
    color: var(--dark-text-color);
}

.dark-mode .close-dialog-button:hover {
    background-color: var(--dark-border-color);
}

.dark-mode .user-balance {
    background-color: var(--dark-background-color);
    border-color: var(--dark-border-color);
    color: var(--dark-text-color);
}
