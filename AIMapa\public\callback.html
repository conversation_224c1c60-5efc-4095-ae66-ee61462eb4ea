<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přih<PERSON>šování - AI Mapa</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }

        .callback-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        p {
            margin: 15px 0;
            line-height: 1.5;
        }

        .dark-mode {
            background-color: #222;
            color: #f5f5f5;
        }

        .dark-mode .callback-container {
            background-color: #333;
            color: #f5f5f5;
        }

        .dark-mode h1 {
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="callback-container">
        <h1>Přihlašování do AI Mapa</h1>
        <div class="loader"></div>
        <p>Probíhá zpracování přihlášení...</p>
        <p>Budete automaticky přesměrováni zpět do aplikace.</p>
    </div>

    <script>
        // Kontrola, zda má být použit tmavý režim
        function checkDarkMode() {
            const isDarkMode = localStorage.getItem('aiMapaDarkMode') === 'true';
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
            }
        }

        // Kontrola tmavého režimu při načtení stránky
        checkDarkMode();

        // Funkce pro získání parametru z URL
        function getParameterByName(name, url = window.location.href) {
            name = name.replace(/[\[\]]/g, '\\$&');
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        // Kontrola, zda máme autentizační kód
        const code = getParameterByName('code');
        const state = getParameterByName('state');
        const error = getParameterByName('error');
        const errorDescription = getParameterByName('error_description');

        // Logování pro debugování
        console.log('Callback parametry:');
        console.log('Code:', code ? 'Přítomen' : 'Chybí');
        console.log('State:', state ? 'Přítomen' : 'Chybí');
        console.log('Error:', error || 'Žádná chyba');

        // Kontrola, zda došlo k chybě
        if (error) {
            console.error('Chyba při přihlašování:', error, errorDescription);
            document.querySelector('.loader').style.display = 'none';
            document.querySelector('p').innerHTML = 'Došlo k chybě při přihlašování: ' + errorDescription;

            // Přesměrování na /overeno po delší prodlevě
            setTimeout(() => {
                window.location.href = '/overeno';
            }, 5000);
        } else if (code && state) {
            // Máme autentizační kód, uložíme informaci o přihlášení
            localStorage.setItem('aiMapaLoggedIn', 'true');
            console.log('Autentizační kód byl úspěšně přijat, přesměrovávám na /overeno...');

            // Přesměrování na /overeno po krátké prodlevě
            setTimeout(() => {
                // Odstranění parametrů z URL při přesměrování
                window.location.href = '/overeno';
            }, 2000);
        } else {
            console.warn('Chybí autentizační kód nebo state, přesměrovávám na /overeno...');

            // Přesměrování na /overeno po krátké prodlevě
            setTimeout(() => {
                window.location.href = '/overeno';
            }, 2000);
        }
    </script>
</body>
</html>
