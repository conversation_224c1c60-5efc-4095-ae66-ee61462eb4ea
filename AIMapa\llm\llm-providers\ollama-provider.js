/**
 * Ollama Provider
 * Verze 0.4.4
 *
 * Provider pro lokální Ollama instance
 */

/**
 * T<PERSON>ída pro komunikaci s Ollama API
 */
class OllamaProvider {
  /**
   * Konstruktor
   * @param {Object} options - Konfigurační objekt
   * @param {string} options.model - Název modelu
   * @param {string} options.baseUrl - URL Ollama serveru
   * @param {number} options.temperature - Teplota (0.0 - 1.0)
   * @param {number} options.maxTokens - Maximální počet tokenů
   */
  constructor(options = {}) {
    this.model = options.model || 'llama3.2';
    this.baseUrl = options.baseUrl || 'http://localhost:11434';
    this.temperature = options.temperature || 0.7;
    this.maxTokens = options.maxTokens || 1000;

    console.log(`Ollama Provider inicializován s modelem ${this.model} na ${this.baseUrl}`);
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> odpovědi od Ollama
   * @param {string} prompt - Prompt pro model
   * @returns {Promise<Object>} Odpověď od modelu
   */
  async getCompletion(prompt) {
    try {
      const url = `${this.baseUrl}/api/generate`;
      
      const requestBody = {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: this.temperature,
          num_predict: this.maxTokens
        }
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Ollama API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (!data.response) {
        throw new Error('Ollama nevrátil žádnou odpověď');
      }

      return {
        text: data.response,
        usage: {
          promptTokens: data.prompt_eval_count || 0,
          completionTokens: data.eval_count || 0,
          totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
        },
        model: this.model,
        provider: 'ollama'
      };
    } catch (error) {
      console.error('Chyba při komunikaci s Ollama:', error);
      throw error;
    }
  }

  /**
   * Získání seznamu dostupných modelů
   * @returns {Promise<Array>} Seznam dostupných modelů
   */
  async getAvailableModels() {
    try {
      const url = `${this.baseUrl}/api/tags`;
      
      const response = await fetch(url, {
        method: 'GET'
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Ollama API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();
      
      return data.models?.map(model => ({
        name: model.name,
        size: model.size,
        digest: model.digest,
        modified_at: model.modified_at
      })) || [];
    } catch (error) {
      console.error('Chyba při získávání seznamu modelů z Ollama:', error);
      throw error;
    }
  }

  /**
   * Test připojení k Ollama
   * @returns {Promise<boolean>} True pokud je připojení úspěšné
   */
  async testConnection() {
    try {
      const url = `${this.baseUrl}/api/version`;
      
      const response = await fetch(url, {
        method: 'GET'
      });

      return response.ok;
    } catch (error) {
      console.error('Test připojení k Ollama selhal:', error);
      return false;
    }
  }

  /**
   * Stažení modelu (pokud není dostupný)
   * @param {string} modelName - Název modelu ke stažení
   * @returns {Promise<boolean>} True pokud je stažení úspěšné
   */
  async pullModel(modelName = null) {
    try {
      const model = modelName || this.model;
      const url = `${this.baseUrl}/api/pull`;
      
      const requestBody = {
        name: model
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Ollama pull chyba: ${response.status} - ${errorData}`);
      }

      console.log(`Model ${model} byl úspěšně stažen`);
      return true;
    } catch (error) {
      console.error('Chyba při stahování modelu z Ollama:', error);
      return false;
    }
  }

  /**
   * Získání informací o modelu
   * @returns {Object} Informace o modelu
   */
  getModelInfo() {
    return {
      provider: 'ollama',
      model: this.model,
      baseUrl: this.baseUrl,
      temperature: this.temperature,
      maxTokens: this.maxTokens
    };
  }

  /**
   * Získání populárních modelů pro Ollama
   * @returns {Array} Seznam populárních modelů
   */
  static getPopularModels() {
    return [
      'llama3.2',
      'llama3.1',
      'llama3',
      'mistral',
      'codellama',
      'phi3',
      'gemma2',
      'qwen2'
    ];
  }
}

module.exports = OllamaProvider;
