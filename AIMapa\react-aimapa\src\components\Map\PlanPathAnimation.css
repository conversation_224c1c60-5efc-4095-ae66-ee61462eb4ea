.plan-path-animation {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 500px;
  z-index: 1000;
}

.plan-path-progress {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.plan-path-progress-bar {
  height: 8px;
  background-color: #3498db;
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.plan-path-progress-text {
  color: white;
  font-size: 14px;
  text-align: center;
  margin-top: 5px;
  font-weight: 500;
}

/* Styly pro markery cesty */
.plan-path-marker {
  background: transparent;
  border: none;
}

.plan-path-marker-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  border: 3px solid white;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: visible;
  z-index: 500;
}

.plan-path-marker-inner:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.plan-path-marker-number {
  font-size: 16px;
  font-weight: bold;
}

.plan-path-marker-pulse {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(52, 152, 219, 0.5);
  animation: markerRipple 2s infinite;
  opacity: 0;
}

.plan-path-marker-inner.active .plan-path-marker-pulse {
  opacity: 1;
}

.plan-path-marker-inner.start {
  background-color: #27ae60;
  transform: scale(1.1);
}

.plan-path-marker-inner.end {
  background-color: #e74c3c;
  transform: scale(1.1);
}

.plan-path-marker-inner.active {
  background-color: #f39c12;
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(243, 156, 18, 0.8);
  z-index: 1000;
}

.plan-path-marker-inner.next {
  animation: markerPulse 1.5s infinite;
}

.plan-path-marker-inner.completed {
  background-color: #27ae60;
}

/* Animovaný marker pro cestu */
.animated-path-marker {
  background: transparent;
  border: none;
}

.animated-path-marker-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e74c3c;
  color: white;
  box-shadow: 0 2px 10px rgba(231, 76, 60, 0.6);
  border: 2px solid white;
  transition: all 0.2s ease;
  animation: markerPulse 1s infinite;
}

.animated-path-marker-inner i {
  font-size: 16px;
}

/* Marker pro vzdálenost */
.distance-marker {
  background: transparent;
  border: none;
}

.distance-marker-inner {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.5s ease;
}

.distance-marker.visible .distance-marker-inner {
  opacity: 1;
  transform: translateY(0);
}

/* Popup pro body cesty */
.path-point-popup {
  min-width: 200px;
}

.path-point-popup-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.path-point-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.path-point-title {
  font-weight: bold;
  font-size: 14px;
}

.path-point-description {
  font-size: 12px;
  color: #555;
  margin-top: 5px;
}

/* Animace */
@keyframes markerPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

@keyframes markerRipple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
