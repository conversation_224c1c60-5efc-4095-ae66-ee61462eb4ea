/* Základní styly pro AIMapa */

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

/* Header */
header {
    background-color: #333;
    color: #fff;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    margin: 0;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 1rem;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
}

nav ul li a:hover {
    text-decoration: underline;
}

/* Main content */
main {
    display: flex;
    height: calc(100vh - 120px);
}

#map-container {
    flex: 1;
    height: 100%;
}

#map {
    width: 100%;
    height: 100%;
}

#sidebar {
    width: 250px;
    background-color: #fff;
    padding: 1rem;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

/* Money indicator */
#money-indicator {
    background-color: #27ae60;
    color: #fff;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: bold;
}

/* Commands menu */
#commands-menu {
    margin-bottom: 1rem;
}

#toggle-commands {
    width: 100%;
    padding: 0.5rem;
    background-color: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#commands-list {
    margin-top: 0.5rem;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#commands-list ul {
    list-style: none;
}

#commands-list ul li {
    padding: 0.5rem;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

#commands-list ul li:last-child {
    border-bottom: none;
}

#commands-list ul li:hover {
    background-color: #f0f0f0;
}

/* Footer */
footer {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 1rem;
}
