/**
 * Styly pro modul zobrazení počasí
 * Verze 0.2.8.6.2
 */

/* Tlačítko pro zobrazení počasí */
.weather-button {
    width: 30px;
    height: 30px;
    background-color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.weather-button:hover {
    background-color: #f4f4f4;
}

.weather-button.active {
    background-color: #4CAF50;
    color: white;
}

.weather-button .icon {
    font-size: 16px;
}

/* Ovládací panel pro výběr vrstvy */
.weather-layer-control {
    position: absolute;
    top: 70px;
    right: 10px;
    width: 250px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    overflow: hidden;
}

.weather-layer-control-header {
    background-color: #4CAF50;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.weather-layer-control-header h3 {
    margin: 0;
    color: white;
    font-size: 16px;
}

.weather-layer-control-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.weather-layer-control-body {
    padding: 15px;
}

/* Možnosti vrstvy počasí */
.weather-layer-options {
    margin-bottom: 15px;
}

.weather-layer-option {
    display: block;
    margin-bottom: 8px;
    cursor: pointer;
}

.weather-layer-option:last-child {
    margin-bottom: 0;
}

.weather-layer-option input {
    margin-right: 8px;
}

/* Nastavení průhlednosti */
.weather-layer-opacity {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.weather-layer-opacity label {
    margin-right: 10px;
    margin-bottom: 5px;
}

.weather-layer-opacity input {
    flex: 1;
    margin-right: 10px;
}

/* Widget s počasím */
.weather-widget {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 250px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow: hidden;
}

.weather-widget-header {
    background-color: #4CAF50;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.weather-widget-header h3 {
    margin: 0;
    color: white;
    font-size: 16px;
}

.weather-widget-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.weather-widget-body {
    padding: 15px;
}

.weather-widget-main {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.weather-widget-icon {
    width: 50px;
    height: 50px;
}

.weather-widget-temp {
    font-size: 28px;
    font-weight: bold;
    margin-left: 10px;
}

.weather-widget-description {
    text-transform: capitalize;
    margin-bottom: 15px;
    font-style: italic;
}

.weather-widget-details {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    padding: 10px;
}

.weather-widget-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.weather-widget-detail:last-child {
    margin-bottom: 0;
}

.weather-widget-detail-label {
    color: #666;
}

.weather-widget-detail-value {
    font-weight: bold;
}

/* Tmavý režim */
body[data-theme="dark"] .weather-button {
    background-color: #333;
    color: white;
}

body[data-theme="dark"] .weather-button:hover {
    background-color: #444;
}

body[data-theme="dark"] .weather-button.active {
    background-color: #4CAF50;
}

body[data-theme="dark"] .weather-layer-control {
    background-color: #333;
    color: white;
}

body[data-theme="dark"] .weather-layer-control-body {
    color: white;
}

body[data-theme="dark"] .weather-widget {
    background-color: #333;
    color: white;
}

body[data-theme="dark"] .weather-widget-details {
    background-color: rgba(255, 255, 255, 0.1);
}

body[data-theme="dark"] .weather-widget-detail-label {
    color: #aaa;
}
