@import "../variables";
@import "../mixins";

/* Funkce */
.features {
  padding: 80px 0;

  h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 32px;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;

    @include respond-to(md) {
      grid-template-columns: 1fr;
    }
  }

  .feature-card {
    @include card(30px);
    transition: $transition;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    h3 {
      margin-bottom: 15px;
      color: $primary-color;
    }
  }
}
