:root {
  /* <PERSON><PERSON><PERSON><PERSON><PERSON> barvy */
  --primary-color: #4285F4;
  --secondary-color: #34A853;
  --accent-color: #FBBC05;
  --error-color: #EA4335;
  --text-color: #333333;
  --light-text-color: #666666;
  --background-color: #FFFFFF;
  --light-background-color: #F5F5F5;
  --border-color: #DDDDDD;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Nové barvy pro vylepšenou správu API */
  --primary-green: #2ecc71;
  --primary-green-dark: #27ae60;
  --primary-green-light: #a9dfbf;

  --primary-red: #e74c3c;
  --primary-red-dark: #c0392b;
  --primary-red-light: #f5b7b1;

  --primary-orange: #f39c12;
  --primary-orange-dark: #d35400;
  --primary-orange-light: #fad7a0;

  --dark-bg: #1e272e;
  --light-bg: #f5f6fa;
  --card-bg: #2c3e50;
  --card-hover: #34495e;

  --text-light: #ecf0f1;
  --text-dark: #2c3e50;
  --text-muted: #95a5a6;
}

.App {
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--dark-bg);
  color: var(--text-light);
  overflow: hidden;
}

.App-header {
  background-color: var(--card-bg);
  padding: 15px 20px;
  color: var(--text-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid var(--border-color);
}

.logo a {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: bold;
  transition: color 0.3s;
}

.logo a:hover {
  color: var(--primary-green-light);
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-nav li {
  margin-left: 20px;
}

.main-nav a {
  color: var(--text-light);
  text-decoration: none;
  padding: 8px 15px;
  border-radius: 5px;
  transition: all 0.3s;
  display: inline-block;
}

.main-nav a:hover {
  background-color: rgba(46, 204, 113, 0.2);
  color: var(--primary-green);
}

.login-button {
  background-color: var(--primary-orange);
  color: var(--text-light);
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: bold;
}

.login-button:hover {
  background-color: var(--primary-orange-dark);
}

.App-main {
  flex: 1;
  padding: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.App-footer {
  background-color: var(--card-bg);
  padding: 5px;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
}

/* Page styles */
.page {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.page h1 {
  margin-bottom: 20px;
  color: var(--primary-green);
  font-size: 2.2rem;
}

/* Home page */
.home-page {
  text-align: center;
}

.home-page p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 30px;
  color: var(--text-light);
}

.features-section {
  margin: 50px 0;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 40px;
  color: var(--primary-orange);
  font-size: 1.8rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.feature-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s, box-shadow 0.3s;
  border-top: 5px solid var(--primary-green);
}

.feature-card:nth-child(2) {
  border-top-color: var(--primary-orange);
}

.feature-card:nth-child(3) {
  border-top-color: var(--primary-red);
}

.feature-card:nth-child(4) {
  border-top-color: var(--primary-color);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.feature-card i {
  font-size: 3rem;
  margin-bottom: 20px;
  display: inline-block;
  transition: transform 0.3s;
}

.feature-card:nth-child(1) i {
  color: var(--primary-green);
}

.feature-card:nth-child(2) i {
  color: var(--primary-orange);
}

.feature-card:nth-child(3) i {
  color: var(--primary-red);
}

.feature-card:nth-child(4) i {
  color: var(--primary-color);
}

.feature-card:hover i {
  transform: scale(1.2);
}

.feature-card h3 {
  margin-bottom: 15px;
  font-size: 1.4rem;
  color: var(--text-light);
}

.feature-card p {
  color: var(--text-muted);
  line-height: 1.6;
}

/* Responsive */
@media (max-width: 768px) {
  .App-header {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
  }

  .logo {
    margin-bottom: 5px;
  }

  .main-nav {
    width: 100%;
  }

  .main-nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }

  .main-nav li {
    margin: 0;
    flex: 1;
    min-width: 100px;
  }

  .main-nav a {
    width: 100%;
    text-align: center;
    padding: 10px 5px;
  }

  .auth-status {
    width: 100%;
  }

  .login-button {
    width: 100%;
    padding: 10px;
  }

  .page {
    padding: 20px 15px;
  }

  .feature-card {
    padding: 20px 15px;
  }
}
