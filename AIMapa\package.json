{"name": "aimapa", "version": "0.4.1", "description": "Interaktivní mapa s AI funkcemi", "main": "server.js", "scripts": {"start": "node server.js", "start:fastify": "node fastify-server.js", "start:all": "node start-servers.js", "dev": "nodemon server.js", "dev:fastify": "nodemon fastify-server.js", "dev:client": "webpack serve --mode development", "dev:all": "concurrently \"npm run dev\" \"npm run dev:fastify\" \"npm run dev:client\"", "build": "cross-env NODE_ENV=production webpack", "build:dev": "webpack", "build:netlify": "node update-csp.js && cross-env NODE_ENV=production webpack && node scripts/copy-assets.js", "update-csp": "node update-csp.js", "test": "node tests/run-tests.js", "test:unit": "node -e \"const MapUtilsTest = require('./tests/unit/map-utils.test'); const results = MapUtilsTest.runAllTests(); console.log(JSON.stringify(results, null, 2));\"", "test:integration": "node -e \"const MapApiTest = require('./tests/integration/map-api.test'); MapApiTest.runAllTests().then(results => console.log(JSON.stringify(results, null, 2)));\"", "test:auth-sync": "node tests/integration/auth0-supabase-sync.test.js", "test:e2e": "node -e \"const MapWorkflowTest = require('./tests/e2e/map-workflow.test'); MapWorkflowTest.runAllScenarios().then(results => console.log(JSON.stringify(results, null, 2)));\"", "test:ai": "node -e \"const AIModelTest = require('./tests/ai/model-evaluation.test'); const results = AIModelTest.runAllTests(); console.log(JSON.stringify(results, null, 2));\"", "test:migration": "node tests/migration-test.js", "test:a11y": "node tests/accessibility/a11y.test.js", "test:llm-performance": "node tests/llm-performance/performance.test.js", "auth0-explorer": "node auth0-terminal-explorer.js", "db:create-users": "node scripts/create-users-table.js", "db:sync-auth0": "node scripts/sync-auth0-users.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "migrate": "node scripts/migrate-to-new-stack.js", "supabase:start": "node start-local-supabase.js", "supabase:stop": "node stop-local-supabase.js", "dev:local": "cross-env USE_LOCAL_SUPABASE=true nodemon server.js", "dev:all:local": "cross-env USE_LOCAL_SUPABASE=true concurrently \"npm run dev\" \"npm run dev:fastify\" \"npm run dev:client\""}, "repository": {"type": "commonjs", "url": "git+https://github.com/l4zorik/AIMapa.git"}, "keywords": ["map", "ai", "interactive"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/l4zorik/AIMapa/issues"}, "homepage": "https://github.com/l4zorik/AIMapa#readme", "engines": {"node": ">=16.x"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.3", "@fastify/auth": "^5.0.2", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.51", "@langchain/openai": "^0.5.10", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "body-parser": "^1.20.2", "chart.js": "^4.4.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-openid-connect": "^2.18.0", "express-rate-limit": "^7.5.0", "fastify": "^5.3.2", "helmet": "^8.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.24", "marked": "^11.1.1", "mongoose": "^7.5.0", "node-fetch": "^2.7.0", "redis": "^4.6.13", "request": "^2.88.2", "stripe": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.1", "axe-core": "^4.10.3", "babel-loader": "^10.0.0", "concurrently": "^8.2.2", "core-js": "^3.36.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "mini-css-extract-plugin": "^2.9.2", "nodemon": "^3.0.1", "open": "^10.1.1", "redis-mock": "^0.56.3", "sass": "^1.87.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1", "workbox-webpack-plugin": "^7.3.0"}}