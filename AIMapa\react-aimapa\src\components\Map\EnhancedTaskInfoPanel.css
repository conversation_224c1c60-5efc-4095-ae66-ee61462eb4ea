/* Styly pro roz<PERSON><PERSON><PERSON><PERSON>ý informační panel úkolu */
:root {
  --marker-primary: #3498db;
  --marker-success: #2ecc71;
  --marker-warning: #f39c12;
  --marker-danger: #e74c3c;
  --marker-info: #9b59b6;
}

.enhanced-task-info-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 350px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 1000;
  animation: slide-in 0.3s ease-out;
  max-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.enhanced-task-info-panel.with-chat {
  height: 500px;
  max-height: calc(100vh - 100px);
}

@keyframes slide-in {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.task-info-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--marker-primary);
  color: white;
}

.task-info-header i {
  font-size: 1.2rem;
  margin-right: 10px;
}

.task-info-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-info-header-actions {
  display: flex;
  gap: 8px;
}

.chat-toggle-button, .close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.chat-toggle-button:hover, .close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.task-info-content {
  padding: 15px;
  overflow-y: auto;
}

.task-info-section {
  margin-bottom: 15px;
}

.task-info-section:last-child {
  margin-bottom: 0;
}

.task-info-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-bottom: 5px;
  color: #34495e;
}

.task-info-label i {
  margin-right: 8px;
  color: #7f8c8d;
}

.task-info-value {
  padding: 5px 0;
}

.task-info-coordinates {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-family: monospace;
}

.task-info-route {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 5px 0;
}

.route-point {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.route-point i {
  color: #7f8c8d;
}

.route-arrow {
  display: flex;
  justify-content: center;
  padding: 2px 0;
  color: #7f8c8d;
}

.task-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s;
}

.task-status:hover {
  opacity: 0.8;
}

.task-status.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}

.task-status.pending {
  background-color: rgba(52, 152, 219, 0.1);
  color: #2980b9;
}

.task-info-actions {
  display: flex;
  gap: 10px;
  padding: 0 15px 15px;
}

.task-info-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.task-info-button.primary {
  background-color: var(--marker-primary);
  color: white;
}

.task-info-button.primary:hover {
  background-color: #2980b9;
}

.task-info-button.secondary {
  background-color: #ecf0f1;
  color: #34495e;
}

.task-info-button.secondary:hover {
  background-color: #bdc3c7;
}

/* Styly pro chat */
.task-chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-top: 1px solid #ecf0f1;
  overflow: hidden;
}

.task-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
}

.task-chat-message {
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: 8px;
  max-width: 85%;
  animation: fade-in 0.3s ease;
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-chat-message.user {
  background-color: #3498db;
  color: white;
  align-self: flex-end;
  margin-left: auto;
}

.task-chat-message.assistant {
  background-color: #ecf0f1;
  color: #34495e;
  align-self: flex-start;
}

.task-chat-message.system {
  background-color: #f8f9fa;
  color: #7f8c8d;
  border: 1px dashed #bdc3c7;
  font-style: italic;
  max-width: 100%;
  text-align: center;
  margin: 10px auto;
  padding: 5px 10px;
}

.task-chat-message.error {
  background-color: #ffecec;
  color: #e74c3c;
  border: 1px solid #e74c3c;
  max-width: 100%;
  margin: 10px auto;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.8rem;
  opacity: 0.8;
}

.message-content {
  word-break: break-word;
}

.task-chat-input-container {
  display: flex;
  padding: 10px;
  background-color: white;
  border-top: 1px solid #ecf0f1;
}

.task-chat-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  resize: none;
  min-height: 40px;
  max-height: 100px;
  font-family: inherit;
}

.task-chat-input:focus {
  outline: none;
  border-color: #3498db;
}

.task-chat-send-button {
  width: 40px;
  height: 40px;
  margin-left: 8px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.task-chat-send-button:hover:not(:disabled) {
  background-color: #2980b9;
}

.task-chat-send-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responzivní design */
@media (max-width: 768px) {
  .enhanced-task-info-panel {
    width: calc(100% - 40px);
    bottom: 10px;
    right: 10px;
    left: 10px;
  }
}
