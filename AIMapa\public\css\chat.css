/* 
 * <PERSON><PERSON><PERSON> - Chat styly
 * Verze 0.3.8.7
 */

/* Chat sekce */
.chat-section {
  padding: 40px 0;
  min-height: calc(100vh - 200px);
}

.chat-section .container {
  display: flex;
  gap: 30px;
}

/* <PERSON><PERSON> k<PERSON>er */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 70vh;
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

/* <PERSON>t h<PERSON> */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--light-bg-color);
  border-bottom: 1px solid var(--border-color);
}

.chat-header h2 {
  margin: 0;
  font-size: 20px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.model-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 14px;
}

/* Chat zpr<PERSON>vy */
.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant, .message.system {
  align-self: flex-start;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.user .message-content {
  background-color: var(--primary-color);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background-color: var(--light-bg-color);
  color: var(--text-color);
  border-bottom-left-radius: 4px;
}

.message.system .message-content {
  background-color: var(--secondary-color);
  color: white;
  border-radius: 8px;
}

.message-content p {
  margin: 0 0 10px 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 10px 0;
}

.message-content code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9em;
}

.message-content ul, .message-content ol {
  margin: 10px 0;
  padding-left: 20px;
}

.message-content a {
  color: inherit;
  text-decoration: underline;
}

.message-content img {
  max-width: 100%;
  border-radius: 4px;
  margin: 10px 0;
}

.message-meta {
  font-size: 12px;
  color: var(--light-text-color);
  margin-top: 4px;
  align-self: flex-end;
}

.message.assistant .message-meta {
  align-self: flex-start;
}

/* Chat vstup */
.chat-input {
  padding: 15px 20px;
  background-color: var(--light-bg-color);
  border-top: 1px solid var(--border-color);
}

.chat-input form {
  display: flex;
  gap: 10px;
}

.chat-input textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  resize: none;
  max-height: 150px;
  background-color: var(--bg-color);
}

.chat-input button {
  align-self: flex-end;
  border-radius: 20px;
  padding: 10px 20px;
}

/* Chat sidebar */
.chat-sidebar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chat-history, .chat-context {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 20px;
}

.chat-history h3, .chat-context h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: var(--primary-color);
}

.chat-history ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-history li {
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-history li:hover {
  background-color: var(--light-bg-color);
}

.chat-history li.active {
  background-color: var(--primary-color);
  color: white;
}

.context-item {
  margin-bottom: 15px;
}

.context-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.context-item input, .context-item select, .context-item textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  background-color: var(--bg-color);
}

.context-item textarea {
  resize: vertical;
  min-height: 60px;
}

/* Načítání */
.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--light-text-color);
  font-style: italic;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--light-text-color);
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Responzivní design */
@media (max-width: 992px) {
  .chat-section .container {
    flex-direction: column;
  }
  
  .chat-sidebar {
    width: 100%;
    flex-direction: row;
  }
  
  .chat-history, .chat-context {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .chat-sidebar {
    flex-direction: column;
  }
  
  .chat-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .chat-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .message {
    max-width: 90%;
  }
}
