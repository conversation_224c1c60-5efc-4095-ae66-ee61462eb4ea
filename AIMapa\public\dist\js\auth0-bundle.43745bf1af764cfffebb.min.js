(()=>{"use strict";var e={};e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),"function"==typeof SuppressedError&&SuppressedError;var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e.g?e.g:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e,t){return e(t={exports:{}},t.exports),t.exports}var o=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){var e=this;this.locked=new Map,this.addToLocked=function(t,n){var r=e.locked.get(t);void 0===r?void 0===n?e.locked.set(t,[]):e.locked.set(t,[n]):void 0!==n&&(r.unshift(n),e.locked.set(t,r))},this.isLocked=function(t){return e.locked.has(t)},this.lock=function(t){return new Promise((function(n,r){e.isLocked(t)?e.addToLocked(t,n):(e.addToLocked(t),n())}))},this.unlock=function(t){var n=e.locked.get(t);if(void 0!==n&&0!==n.length){var r=n.pop();e.locked.set(t,n),void 0!==r&&setTimeout(r,0)}else e.locked.delete(t)}}return e.getInstance=function(){return void 0===e.instance&&(e.instance=new e),e.instance},e}();t.default=function(){return n.getInstance()}}));n(o);var i=n(r((function(e,n){var r=t&&t.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{s(r.next(e))}catch(e){i(e)}}function c(e){try{s(r.throw(e))}catch(e){i(e)}}function s(e){e.done?o(e.value):new n((function(t){t(e.value)})).then(a,c)}s((r=r.apply(e,t||[])).next())}))},i=t&&t.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}},a=t;Object.defineProperty(n,"__esModule",{value:!0});var c="browser-tabs-lock-key",s={key:function(e){return r(a,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},getItem:function(e){return r(a,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},clear:function(){return r(a,void 0,void 0,(function(){return i(this,(function(e){return[2,window.localStorage.clear()]}))}))},removeItem:function(e){return r(a,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},setItem:function(e,t){return r(a,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},keySync:function(e){return window.localStorage.key(e)},getItemSync:function(e){return window.localStorage.getItem(e)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(e){return window.localStorage.removeItem(e)},setItemSync:function(e,t){return window.localStorage.setItem(e,t)}};function u(e){return new Promise((function(t){return setTimeout(t,e)}))}function l(e){for(var t="",n=0;n<e;n++)t+="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz"[Math.floor(61*Math.random())];return t}var d=function(){function e(t){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+l(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=t,void 0===e.waiters&&(e.waiters=[])}return e.prototype.acquireLock=function(t,n){return void 0===n&&(n=5e3),r(this,void 0,void 0,(function(){var r,o,a,d,f,h,v;return i(this,(function(i){switch(i.label){case 0:r=Date.now()+l(4),o=Date.now()+n,a=c+"-"+t,d=void 0===this.storageHandler?s:this.storageHandler,i.label=1;case 1:return Date.now()<o?[4,u(30)]:[3,8];case 2:return i.sent(),null!==d.getItemSync(a)?[3,5]:(f=this.id+"-"+t+"-"+r,[4,u(Math.floor(25*Math.random()))]);case 3:return i.sent(),d.setItemSync(a,JSON.stringify({id:this.id,iat:r,timeoutKey:f,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,u(30)];case 4:return i.sent(),null!==(h=d.getItemSync(a))&&(v=JSON.parse(h)).id===this.id&&v.iat===r?(this.acquiredIatSet.add(r),this.refreshLockWhileAcquired(a,r),[2,!0]):[3,7];case 5:return e.lockCorrector(void 0===this.storageHandler?s:this.storageHandler),[4,this.waitForSomethingToChange(o)];case 6:i.sent(),i.label=7;case 7:return r=Date.now()+l(4),[3,1];case 8:return[2,!1]}}))}))},e.prototype.refreshLockWhileAcquired=function(e,t){return r(this,void 0,void 0,(function(){var n=this;return i(this,(function(a){return setTimeout((function(){return r(n,void 0,void 0,(function(){var n,r,a;return i(this,(function(i){switch(i.label){case 0:return[4,o.default().lock(t)];case 1:return i.sent(),this.acquiredIatSet.has(t)?(n=void 0===this.storageHandler?s:this.storageHandler,null===(r=n.getItemSync(e))?(o.default().unlock(t),[2]):((a=JSON.parse(r)).timeRefreshed=Date.now(),n.setItemSync(e,JSON.stringify(a)),o.default().unlock(t),this.refreshLockWhileAcquired(e,t),[2])):(o.default().unlock(t),[2])}}))}))}),1e3),[2]}))}))},e.prototype.waitForSomethingToChange=function(t){return r(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return[4,new Promise((function(n){var r=!1,o=Date.now(),i=!1;function a(){if(i||(window.removeEventListener("storage",a),e.removeFromWaiting(a),clearTimeout(c),i=!0),!r){r=!0;var t=50-(Date.now()-o);t>0?setTimeout(n,t):n(null)}}window.addEventListener("storage",a),e.addToWaiting(a);var c=setTimeout(a,Math.max(0,t-Date.now()))}))];case 1:return n.sent(),[2]}}))}))},e.addToWaiting=function(t){this.removeFromWaiting(t),void 0!==e.waiters&&e.waiters.push(t)},e.removeFromWaiting=function(t){void 0!==e.waiters&&(e.waiters=e.waiters.filter((function(e){return e!==t})))},e.notifyWaiters=function(){void 0!==e.waiters&&e.waiters.slice().forEach((function(e){return e()}))},e.prototype.releaseLock=function(e){return r(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return[4,this.releaseLock__private__(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.releaseLock__private__=function(t){return r(this,void 0,void 0,(function(){var n,r,a,u;return i(this,(function(i){switch(i.label){case 0:return n=void 0===this.storageHandler?s:this.storageHandler,r=c+"-"+t,null===(a=n.getItemSync(r))?[2]:(u=JSON.parse(a)).id!==this.id?[3,2]:[4,o.default().lock(u.iat)];case 1:i.sent(),this.acquiredIatSet.delete(u.iat),n.removeItemSync(r),o.default().unlock(u.iat),e.notifyWaiters(),i.label=2;case 2:return[2]}}))}))},e.lockCorrector=function(t){for(var n=Date.now()-5e3,r=t,o=[],i=0;;){var a=r.keySync(i);if(null===a)break;o.push(a),i++}for(var s=!1,u=0;u<o.length;u++){var l=o[u];if(l.includes(c)){var d=r.getItemSync(l);if(null!==d){var f=JSON.parse(d);(void 0===f.timeRefreshed&&f.timeAcquired<n||void 0!==f.timeRefreshed&&f.timeRefreshed<n)&&(r.removeItemSync(l),s=!0)}}}s&&e.notifyWaiters()},e.waiters=void 0,e}();n.default=d})));Error;var a=r((function(e,n){var r=t&&t.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};function o(e,t){if(!t)return"";var n="; "+e;return!0===t?n:n+"="+t}function i(e,t,n){return encodeURIComponent(e).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(t).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(e){if("number"==typeof e.expires){var t=new Date;t.setMilliseconds(t.getMilliseconds()+864e5*e.expires),e.expires=t}return o("Expires",e.expires?e.expires.toUTCString():"")+o("Domain",e.domain)+o("Path",e.path)+o("Secure",e.secure)+o("SameSite",e.sameSite)}(n)}function a(e){for(var t={},n=e?e.split("; "):[],r=/(%[\dA-F]{2})+/gi,o=0;o<n.length;o++){var i=n[o].split("="),a=i.slice(1).join("=");'"'===a.charAt(0)&&(a=a.slice(1,-1));try{t[i[0].replace(r,decodeURIComponent)]=a.replace(r,decodeURIComponent)}catch(e){}}return t}function c(){return a(document.cookie)}function s(e,t,n){document.cookie=i(e,t,r({path:"/"},n))}n.__esModule=!0,n.encode=i,n.parse=a,n.getAll=c,n.get=function(e){return c()[e]},n.set=s,n.remove=function(e,t){s(e,"",r(r({},t),{expires:-1}))}}));n(a),a.encode,a.parse,a.getAll,a.get,a.set,a.remove,new i,process.env.AUTH0_DOMAIN,process.env.AUTH0_CLIENT_ID,window.location.origin,process.env.AUTH0_AUDIENCE})();
//# sourceMappingURL=auth0-bundle.43745bf1af764cfffebb.min.js.map