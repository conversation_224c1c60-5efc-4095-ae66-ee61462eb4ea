/**
 * St<PERSON>y pro přihlašovací obrazovku AIMapa
 * Verze 0.3.8.4
 */

/* <PERSON><PERSON><PERSON><PERSON>šovací obrazovka */
.auth-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999; /* Vyšší z-index než cokoli jiného */
    backdrop-filter: blur(5px); /* Rozmazání pozadí */
}

/* Kontejner pro přihlašovací formulář */
.auth-container {
    width: 100%;
    max-width: 500px;
    background-color: var(--background-color, white);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* Hlav<PERSON><PERSON>ka */
.auth-header {
    padding: 30px 20px;
    text-align: center;
    background-color: var(--primary-color, #3498db);
    color: white;
}

.auth-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
}

.auth-header p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

/* Z<PERSON>lo<PERSON>ky */
.auth-tabs {
    display: flex;
    background-color: var(--background-color, white);
    border-bottom: 1px solid var(--border-color, #ddd);
}

.auth-tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color, #333);
    transition: all 0.3s;
}

.auth-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.auth-tab.active {
    border-bottom-color: var(--primary-color, #3498db);
    color: var(--primary-color, #3498db);
}

/* Obsah */
.auth-content {
    padding: 20px;
}

/* Formuláře */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color, #333);
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    border-color: var(--primary-color, #3498db);
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Akce formuláře */
.form-actions {
    margin-top: 30px;
}

.auth-button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color, #3498db);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.auth-button:hover {
    background-color: var(--primary-color-hover, #2980b9);
}

/* Zprávy */
.auth-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
}

.auth-message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.auth-message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.auth-message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* Patička */
.auth-footer {
    padding: 15px;
    text-align: center;
    background-color: var(--background-color-secondary, #f5f5f5);
    border-top: 1px solid var(--border-color, #ddd);
    color: var(--text-color-secondary, #777);
    font-size: 14px;
}

/* Tmavý režim */
body[data-theme="dark"] .auth-container {
    background-color: var(--dark-background, #222);
}

body[data-theme="dark"] .auth-tabs {
    background-color: var(--dark-background, #222);
    border-bottom-color: var(--dark-border, #444);
}

body[data-theme="dark"] .auth-tab {
    color: var(--dark-text, #eee);
}

body[data-theme="dark"] .auth-tab.active {
    color: var(--primary-color, #3498db);
}

body[data-theme="dark"] .form-group label {
    color: var(--dark-text, #eee);
}

body[data-theme="dark"] .form-group input {
    background-color: var(--dark-input-background, #333);
    border-color: var(--dark-border, #444);
    color: var(--dark-text, #eee);
}

body[data-theme="dark"] .auth-footer {
    background-color: var(--dark-background-secondary, #1a1a1a);
    border-top-color: var(--dark-border, #444);
    color: var(--dark-text-secondary, #999);
}

/* Responzivní design */
@media (max-width: 600px) {
    .auth-container {
        width: 90%;
        max-width: none;
        margin: 0 20px;
    }

    .auth-header {
        padding: 20px 15px;
    }

    .auth-header h1 {
        font-size: 24px;
    }

    .auth-tab {
        padding: 12px;
        font-size: 14px;
    }

    .auth-content {
        padding: 15px;
    }

    .form-group input {
        padding: 10px;
        font-size: 14px;
    }

    .auth-button {
        padding: 10px;
        font-size: 14px;
    }
}
