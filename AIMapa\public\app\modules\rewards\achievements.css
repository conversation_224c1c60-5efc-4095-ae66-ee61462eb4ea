/**
 * Styly pro modul achievementů
 * Verze 0.3.7.0
 */

/* Dialog achievementů */
.achievements-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    background-color: #f5f7fa;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.achievements-dialog.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.achievements-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 20px 20px 0 0;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.achievements-dialog-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
}

.achievements-dialog-header h2 {
    margin: 0;
    font-size: 2.2rem;
    color: white;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
}

.achievements-stats {
    display: flex;
    gap: 15px;
    margin-left: auto;
    margin-right: 25px;
}

.achievements-stats-item {
    background-color: rgba(255, 255, 255, 0.15);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.achievements-stats-item:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.achievements-dialog-close {
    background-color: rgba(255, 255, 255, 0.15);
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.achievements-dialog-close:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: #ff4d4d;
    transform: rotate(90deg);
}

.achievements-dialog-content {
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
    flex: 1;
    background-color: #f5f7fa;
}

/* Kategorie achievementů */
.achievements-categories {
    display: flex;
    overflow-x: auto;
    padding: 20px 25px;
    background-color: #ffffff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    gap: 15px;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
    position: relative;
}

.achievements-categories::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 80px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    pointer-events: none;
    z-index: 1;
}

.achievements-categories::-webkit-scrollbar {
    height: 6px;
}

.achievements-categories::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.achievements-categories::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.achievements-category-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    background-color: #fff;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
    white-space: nowrap;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
    min-width: 140px;
    justify-content: center;
    font-weight: 600;
    font-size: 1.05rem;
    color: #555;
}

.achievements-category-item:hover {
    background-color: #f8f9ff;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
    border-color: rgba(74, 137, 220, 0.3);
    color: #4776E6;
}

.achievements-category-item.active {
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 6px 15px rgba(74, 137, 220, 0.25);
    transform: translateY(-3px) scale(1.05);
}

.achievements-category-icon {
    margin-right: 12px;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.achievements-category-item:hover .achievements-category-icon {
    transform: scale(1.1);
}

.achievements-category-item.active .achievements-category-icon {
    transform: scale(1.2);
}

/* Seznam achievementů */
.achievements-list {
    flex: 1;
    overflow-y: auto;
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 25px;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    background-color: #f5f7fa;
}

.achievements-list::-webkit-scrollbar {
    width: 8px;
}

.achievements-list::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.achievements-list::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.achievement-item {
    display: flex;
    background-color: #fff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: hidden;
}

.achievement-item:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.achievement-item.completed {
    border-color: rgba(76, 175, 80, 0.3);
    background-color: #fff;
}

.achievement-item.completed::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 70px 70px 0;
    border-color: transparent #4caf50 transparent transparent;
    z-index: 1;
}

.achievement-item.completed::after {
    content: '✓';
    position: absolute;
    top: 12px;
    right: 12px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    z-index: 2;
}

.achievement-icon {
    font-size: 2.8rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    border-radius: 16px 0 0 16px;
    flex-shrink: 0;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.achievement-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
}

.achievement-item.completed .achievement-icon {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
    box-shadow: 4px 0 10px rgba(76, 175, 80, 0.1);
}

.achievement-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px;
    padding-right: 30px;
}

.achievement-name {
    font-size: 1.5rem;
    font-weight: 800;
    color: #333;
    margin-bottom: 5px;
    position: relative;
    display: inline-block;
}

.achievement-item.completed .achievement-name {
    color: #2e7d32;
}

.achievement-name::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 3px;
}

.achievement-item.completed .achievement-name::after {
    background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
}

.achievement-description {
    font-size: 1.05rem;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.achievement-progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.achievement-progress-bar {
    flex: 1;
    height: 12px;
    background-color: #eee;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.achievement-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 6px;
    transition: width 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    overflow: hidden;
}

.achievement-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    animation: shimmer 2s infinite;
    transform: translateX(-100%);
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.achievement-item.completed .achievement-progress-fill {
    background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
}

.achievement-progress-text {
    font-size: 1rem;
    color: #555;
    white-space: nowrap;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 5px 12px;
    border-radius: 20px;
}

.achievement-item.completed .achievement-progress-text {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.achievement-completed {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    color: #4caf50;
    font-weight: bold;
    margin-top: 8px;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    width: fit-content;
}

.achievement-completed-icon {
    font-size: 1.3rem;
}

.achievement-reward {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding: 20px 25px;
    border-left: 2px solid rgba(0, 0, 0, 0.05);
    min-width: 220px;
    background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
    border-radius: 0 16px 16px 0;
    position: relative;
    overflow: hidden;
}

.achievement-item.completed .achievement-reward {
    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e9 100%);
    border-left-color: rgba(76, 175, 80, 0.3);
}

.achievement-reward::before {
    content: '🏆';
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 5rem;
    opacity: 0.1;
    transform: rotate(15deg);
}

.achievement-item.completed .achievement-reward::before {
    content: '🏆';
    opacity: 0.15;
}

.achievement-reward-title {
    font-size: 1.1rem;
    color: #444;
    margin-bottom: 15px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 8px;
}

.achievement-reward-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 3px;
}

.achievement-item.completed .achievement-reward-title::after {
    background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
}

.achievement-reward-list {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 1.05rem;
    width: 100%;
}

.achievement-reward-list li {
    margin-bottom: 10px;
    padding: 12px 15px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    font-weight: 600;
}

.achievement-reward-list li:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.08);
}

.achievement-reward-list li::before {
    content: '🎁';
    font-size: 1.4rem;
    min-width: 24px;
    text-align: center;
}

.achievement-reward-list li:nth-child(1)::before {
    content: '⭐';
}

.achievement-reward-list li:nth-child(2)::before {
    content: '💰';
}

.achievement-reward-list li:nth-child(3)::before {
    content: '🎯';
}

/* Hvězdička pro odměnu */
.achievement-star {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 2rem;
    z-index: 3;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

.achievement-star.empty {
    opacity: 0.3;
    font-size: 1.8rem;
}

.achievement-item:hover .achievement-star {
    transform: rotate(15deg) scale(1.2);
}

/* Notifikace o dokončení achievementu */
.achievement-notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 400px;
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    transform: translateX(120%);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.achievement-notification.show {
    transform: translateX(0);
}

.achievement-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 20px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    position: relative;
    overflow: hidden;
}

.achievement-notification-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
}

.achievement-notification-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 800;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.achievement-notification-close {
    background-color: rgba(255, 255, 255, 0.15);
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #fff;
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.achievement-notification-close:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: #ff4d4d;
    transform: rotate(90deg);
}

.achievement-notification-content {
    padding: 20px;
}

.achievement-notification-content h3 {
    margin: 0 0 12px 0;
    font-size: 1.4rem;
    color: #333;
    font-weight: 800;
    position: relative;
    display: inline-block;
    padding-bottom: 8px;
}

.achievement-notification-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
    border-radius: 3px;
}

.achievement-notification-content p {
    margin: 0 0 18px 0;
    font-size: 1rem;
    color: #555;
    line-height: 1.5;
}

.achievement-notification-reward {
    background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.achievement-notification-reward::before {
    content: '🏆';
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 5rem;
    opacity: 0.05;
    transform: rotate(15deg);
}

.achievement-notification-reward p {
    margin: 0 0 10px 0;
    font-size: 0.95rem;
    color: #555;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.achievement-notification-reward ul {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 1rem;
}

.achievement-notification-reward ul li {
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.achievement-notification-reward ul li:hover {
    transform: translateX(5px);
}

.achievement-notification-reward ul li::before {
    content: '🎁';
    font-size: 1.2rem;
}

.achievement-notification-reward ul li:nth-child(1)::before {
    content: '⭐';
}

.achievement-notification-reward ul li:nth-child(2)::before {
    content: '💰';
}

.achievement-notification-details {
    display: block;
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
    color: #fff;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 4px 10px rgba(74, 137, 220, 0.2);
    position: relative;
    overflow: hidden;
}

.achievement-notification-details::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
}

.achievement-notification-details:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(74, 137, 220, 0.3);
}

/* Tmavý režim */
body.dark-mode .achievements-dialog {
    background-color: #222;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
}

body.dark-mode .achievements-dialog-header {
    background-color: #333;
    border-bottom-color: #444;
}

body.dark-mode .achievements-dialog-header h2 {
    color: #fff;
}

body.dark-mode .achievements-dialog-close {
    color: #ccc;
}

body.dark-mode .achievements-dialog-close:hover {
    color: #ff6b6b;
}

body.dark-mode .achievements-categories {
    background-color: #333;
    border-bottom-color: #444;
}

body.dark-mode .achievements-category-item {
    background-color: #444;
    border-color: #555;
    color: #eee;
}

body.dark-mode .achievements-category-item:hover {
    background-color: #555;
}

body.dark-mode .achievements-category-item.active {
    background-color: #4a89dc;
    border-color: #4a89dc;
}

body.dark-mode .achievement-item {
    background-color: #333;
    border-color: #444;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body.dark-mode .achievement-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

body.dark-mode .achievement-icon {
    background-color: #444;
}

body.dark-mode .achievement-name {
    color: #eee;
}

body.dark-mode .achievement-description {
    color: #bbb;
}

body.dark-mode .achievement-progress-bar {
    background-color: #444;
}

body.dark-mode .achievement-progress-text {
    color: #bbb;
}

body.dark-mode .achievement-reward {
    border-left-color: #444;
}

body.dark-mode .achievement-reward-title {
    color: #bbb;
}

body.dark-mode .achievement-notification {
    background-color: #333;
}

body.dark-mode .achievement-notification-content h3 {
    color: #eee;
}

body.dark-mode .achievement-notification-content p {
    color: #bbb;
}

body.dark-mode .achievement-notification-reward {
    background-color: #444;
}

body.dark-mode .achievement-notification-reward p {
    color: #bbb;
}

/* Responzivní design */
@media (max-width: 768px) {
    .achievements-dialog {
        width: 95%;
        max-height: 90vh;
    }

    .achievement-item {
        flex-direction: column;
    }

    .achievement-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .achievement-reward {
        border-left: none;
        border-top: 1px solid #eee;
        padding-left: 0;
        padding-top: 10px;
        margin-top: 10px;
        align-items: flex-start;
    }

    body.dark-mode .achievement-reward {
        border-top-color: #444;
    }

    .achievement-notification {
        width: 90%;
        max-width: 350px;
        left: 50%;
        transform: translateX(-50%) translateY(120%);
    }

    .achievement-notification.show {
        transform: translateX(-50%) translateY(0);
    }
}
