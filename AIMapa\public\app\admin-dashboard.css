/**
 * Admin Dashboard Styly
 * Verze 0.3.8.5
 */

#admin-dashboard {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, sans-serif;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.admin-actions {
    display: flex;
    gap: 1rem;
}

.admin-actions button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.admin-actions button:hover {
    background-color: #0056b3;
}

.admin-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

/* Metriky */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-card h3 {
    margin: 0 0 1rem;
    color: #2c3e50;
    font-size: 1.2rem;
}

.metric-card p {
    margin: 0.5rem 0;
    color: #34495e;
}

.metric-card ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

/* Uživatelská tabulka */
.users-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.users-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.users-table tr:hover {
    background-color: #f8f9fa;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.role-select {
    padding: 0.3rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

/* Tlačítka a akce */
.edit-user {
    padding: 0.3rem 0.8rem;
    border: none;
    border-radius: 4px;
    background-color: #6c757d;
    color: white;
    cursor: pointer;
}

.edit-user:hover {
    background-color: #5a6268;
}

/* Grafy */
#user-activity-chart {
    width: 100%;
    height: 300px;
    margin-top: 1rem;
}

/* Zprávy */
.error-message,
.success-message {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    animation: slideIn 0.3s ease-out;
    z-index: 1000;
}

.error-message {
    background-color: #dc3545;
    color: white;
}

.success-message {
    background-color: #28a745;
    color: white;
}

/* Animace */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading stav */
.loading {
    position: relative;
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responzivní design */
@media (max-width: 1200px) {
    .admin-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    #admin-dashboard {
        padding: 1rem;
    }

    .admin-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .users-table {
        display: block;
        overflow-x: auto;
    }
}