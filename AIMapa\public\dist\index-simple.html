<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIMapa</title>
    
    <!-- Leaflet dependencies -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        #app {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        #user-menu {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        #auth-status {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        #login-status {
            margin-right: 10px;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        a:hover {
            background-color: #f0f0f0;
        }
        
        #main-content {
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        #map {
            width: 100%;
            height: 500px;
            margin-top: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" 
      integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" 
      crossorigin="anonymous" referrerpolicy="no-referrer" />


    

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    

    <!-- Browser compatibility polyfills -->
    <script src="/js/polyfills.js"></script>

    <!-- Enhanced Security Policy with unsafe-eval for necessary scripts -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; script-src 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; img-src 'self' data: https://*.auth0.com https://api.mapy.cz https://unpkg.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://cdn.auth0.com https://via.placeholder.com https://*.googleusercontent.com; connect-src 'self' https://*.auth0.com https://*.supabase.co https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://dev-zxj8pir0moo4pdk7.us.auth0.com https://*.stripe.com https://*.googleapis.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; frame-src 'self' https://*.auth0.com https://*.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com; worker-src 'self' blob:; manifest-src 'self'; font-src 'self' data: https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://cdnjs.cloudflare.com; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content; script-src-elem 'unsafe-eval' 'unsafe-inline' 'self' https://*.auth0.com https://cdn.auth0.com https://cdn.jsdelivr.net https://api.mapy.cz https://unpkg.com https://*.openstreetmap.org https://*.openrouteservice.org https://*.freemap.sk https://*.windy.com https://cdnjs.cloudflare.com https://js.stripe.com https://*.coinbase.com https://*.walletconnect.com https://*.solana.com">
</head>
<body>
    <div id="app">
        <!-- User menu container -->
        <div id="user-menu">
            <div id="auth-status">
                <span id="login-status">Kontrola přihlášení...</span>
                <a href="/login" id="login-btn" style="display:none;">Přihlásit se</a>
                <a href="/profile" id="profile-btn" style="display:none;">Profil</a>
                <a href="/logout" id="logout-btn" style="display:none;">Odhlásit se</a>
            </div>
        </div>

        <!-- Main content -->
        <main id="main-content">
            <!-- Content will be dynamically inserted here -->
            <h1>AI Mapa</h1>
            <p>Vítejte v aplikaci AI Mapa!</p>

            <!-- Map container -->
            <div id="map"></div>
        </main>
    </div>

    <script>
        // Inicializace mapy
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializace Leaflet mapy
            const map = L.map('map').setView([50.0755, 14.4378], 13); // Praha
            
            // Přidání OpenStreetMap vrstvy
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            // Kontrola, zda je uživatel přihlášen pomocí localStorage
            const isLoggedIn = localStorage.getItem('aiMapaLoggedIn') === 'true';
            const loginStatus = document.getElementById('login-status');
            const loginBtn = document.getElementById('login-btn');
            const profileBtn = document.getElementById('profile-btn');
            const logoutBtn = document.getElementById('logout-btn');
            
            if (isLoggedIn) {
                loginStatus.textContent = 'Přihlášen';
                loginBtn.style.display = 'none';
                profileBtn.style.display = 'inline-block';
                logoutBtn.style.display = 'inline-block';
            } else {
                loginStatus.textContent = 'Nepřihlášen';
                loginBtn.style.display = 'inline-block';
                profileBtn.style.display = 'none';
                logoutBtn.style.display = 'none';
            }
            
            // Přidání posluchače události pro odhlášení
            logoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                localStorage.removeItem('aiMapaLoggedIn');
                window.location.href = '/';
            });
        });
    </script>
</body>
</html>
