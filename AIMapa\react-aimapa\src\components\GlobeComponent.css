.globe-component {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
}

.globe {
  width: 100%;
  height: 100%;
  min-height: 400px;
  z-index: 1;
}

.globe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  z-index: 2;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Styly pro markery na glóbusu */
.globe-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: red;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  cursor: pointer;
}

/* Styly pro popisky markerů */
.globe-marker-label {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  transform: translate(-50%, -100%);
  margin-top: -10px;
}

/* Styly pro trasy na glóbusu */
.globe-arc {
  pointer-events: none;
}

/* Ovládací prvky glóbusu */
.globe-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 3;
}

.globe-control-button {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.globe-control-button:hover {
  background-color: rgba(52, 152, 219, 0.7);
}

/* Responzivní design */
@media (max-width: 768px) {
  .globe-controls {
    top: auto;
    bottom: 10px;
    right: 10px;
  }
}
