/**
 * AIMapa - Server
 * Verze 0.3.8.7
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
// const { auth } = require('express-openid-connect');
const supabaseService = require('./supabase-service');
// const Auth0Service = require('./auth/auth0-service');
// const createAuth0Routes = require('./auth/auth0-routes');
// const SupabaseAuthSync = require('./auth/supabase-auth-sync');
const LocalAuth = require('./auth/local-auth');

// Middleware imports
const { errorHandler } = require('./middleware/errorHandler');
const { apiLimiter, authLimiter, helmetConfig } = require('./middleware/security');
const APILogger = require('./middleware/apiLogger');

// Načtení .env souboru
if (process.env.NODE_ENV === 'production') {
    require('dotenv').config({ path: '.env.production' });
    console.log('Načteny produkční proměnné prostředí z .env.production');
} else {
    require('dotenv').config();
    console.log('Načteny vývojové proměnné prostředí z .env');
}

// Kontrola načtení proměnných prostředí
console.log('Kontrola načtení proměnných prostředí:');
console.log('AUTH0_DOMAIN:', process.env.AUTH0_DOMAIN ? 'Načteno' : 'Chybí');
console.log('AUTH0_CLIENT_ID:', process.env.AUTH0_CLIENT_ID ? 'Načteno' : 'Chybí');
console.log('AUTH0_CLIENT_SECRET:', process.env.AUTH0_CLIENT_SECRET ? 'Načteno' : 'Chybí');
console.log('AUTH0_AUDIENCE:', process.env.AUTH0_AUDIENCE ? 'Načteno' : 'Chybí');
console.log('AUTH0_CALLBACK_URL:', process.env.AUTH0_CALLBACK_URL ? 'Načteno' : 'Chybí');
console.log('AUTH0_LOGOUT_URL:', process.env.AUTH0_LOGOUT_URL ? 'Načteno' : 'Chybí');
console.log('AUTH0_SCOPE:', process.env.AUTH0_SCOPE ? 'Načteno' : 'Chybí');

const app = express();

// Základní middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(helmetConfig);

// API Logger
const apiLogger = new APILogger({
    logToConsole: process.env.NODE_ENV !== 'production',
    logToSupabase: true,
    excludePaths: ['/health', '/metrics', '/_next', '/static', '/auth/status', '/auth/debug']
});
app.use(apiLogger.middleware());

// Rate limiting
app.use('/api/', apiLimiter);
app.use(['/login', '/callback', '/register'], authLimiter);

// Inicializace lokální autentizace pro vývoj
const localAuth = new LocalAuth({
    secret: process.env.AUTH0_SECRET || 'local-dev-secret-key'
});

// Použití lokální autentizace
app.use(localAuth.getSessionMiddleware());
app.use('/auth', localAuth.createRoutes());

// Lokální auth middleware
const requiresAuth = localAuth.requireAuth.bind(localAuth);

// Endpoint pro zobrazení profilu uživatele
app.get('/profile', requiresAuth, async (req, res) => {
  try {
    const user = localAuth.getUser(req);

    const profile = {
      local: user,
      message: 'Lokální autentizace pro vývoj'
    };

    res.send(JSON.stringify(profile, null, 2));
  } catch (error) {
    console.error('Chyba při získávání profilu:', error);
    res.status(500).json({
      error: 'Chyba při získávání profilu',
      message: error.message
    });
  }
});

// Endpoint pro kontrolu stavu přihlášení
app.get('/auth/status', async (req, res) => {
  try {
    const isAuthenticated = localAuth.isAuthenticated(req);
    let userData = null;

    if (isAuthenticated) {
      userData = localAuth.getUser(req);
    }

    res.json({
      isAuthenticated,
      user: userData
    });
  } catch (error) {
    console.error('Chyba při kontrole stavu přihlášení:', error);
    res.status(500).json({
      error: 'Chyba při kontrole stavu přihlášení',
      message: error.message
    });
  }
});

// Supabase middleware - přidání klienta do req objektu
app.use((req, res, next) => {
    req.supabaseClient = supabaseService.getClient();
    next();
});

// LLM Service a Routes
const LLMService = require('./llm/llm-service');
const createLLMRoutes = require('./llm/llm-routes');

// Inicializace LLM Service
const llmService = new LLMService({
    provider: process.env.LLM_PROVIDER || 'openai',
    model: process.env.LLM_MODEL || 'gpt-4',
    temperature: parseFloat(process.env.LLM_TEMPERATURE) || 0.7,
    maxTokens: parseInt(process.env.LLM_MAX_TOKENS) || 1000,
    cache: process.env.LLM_CACHE_ENABLED === 'true',
    cacheExpiration: parseInt(process.env.LLM_CACHE_EXPIRATION) || 3600
});

// API Routes s rate limitingem
app.use('/api', require('./routes/api'));
app.use('/api/admin', require('./routes/admin'));
app.use('/api/stripe', require('./routes/stripe'));
app.use('/api/llm', createLLMRoutes(llmService, localAuth));

// Statické soubory
app.use(express.static(path.join(__dirname, 'public')));

// Error handling middleware
app.use(errorHandler);

// Zdravotní stav aplikace
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        version: process.env.npm_package_version || '0.4.1',
        environment: process.env.NODE_ENV,
        auth0: {
            configured: !!(process.env.AUTH0_DOMAIN && process.env.AUTH0_CLIENT_ID && process.env.AUTH0_CLIENT_SECRET),
            domain: process.env.AUTH0_DOMAIN
        },
        supabase: {
            configured: !!(process.env.SUPABASE_URL && (process.env.SUPABASE_KEY || process.env.SUPABASE_SERVICE_KEY)),
            url: process.env.SUPABASE_URL
        }
    });
});

// Metriky aplikace (pouze pro adminy)
app.get('/metrics', async (req, res) => {
    try {
        if (!req.oidc.isAuthenticated() ||
            !req.oidc.user['https://aimapa.cz/roles'].includes('admin')) {
            return res.status(403).json({ error: 'Unauthorized' });
        }

        const metrics = await supabaseService.getApplicationMetrics();
        res.json(metrics);
    } catch (error) {
        console.error('Error fetching metrics:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// Endpoint pro získání Auth0 konfigurace (pouze clientId a domain pro klienta)
app.get('/auth/config', (_req, res) => {
    // Určení správné URL pro přesměrování na základě prostředí
    let callbackUrl = process.env.AUTH0_CALLBACK_URL || '';
    res.json({
        domain: process.env.AUTH0_DOMAIN,
        clientId: process.env.AUTH0_CLIENT_ID,
        audience: process.env.AUTH0_AUDIENCE,
        scope: process.env.AUTH0_SCOPE,
        callbackUrl: callbackUrl,
        logoutUrl: process.env.AUTH0_LOGOUT_URL
    });
});

// Spuštění serveru
const port = process.env.PORT || 3000;
app.listen(port, () => {
    console.log(`Server běží na portu ${port}`);
    console.log('Prostředí:', process.env.NODE_ENV);
    console.log('Auth0 Domain:', process.env.AUTH0_DOMAIN);
    console.log('Supabase URL:', process.env.SUPABASE_URL);
});
