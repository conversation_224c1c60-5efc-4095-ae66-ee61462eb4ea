/**
 * Skript pro kopírování statických souborů do dist složky
 */

const fs = require('fs');
const path = require('path');

// Funkce pro rekurzivní kopírování adresáře
function copyDirRecursive(src, dest) {
  // Vytvoření cílového ad<PERSON>ř<PERSON>, pokud neexistuje
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // Načtení obsahu zdrojového adresáře
  const entries = fs.readdirSync(src, { withFileTypes: true });

  // Kopírování každého souboru/adresáře
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // Rekurzivní kopírování podadresáře
      copyDirRecursive(srcPath, destPath);
    } else {
      // Kopírování souboru
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Hlavní funkce
function main() {
  console.log('Kopíruji statické soubory do dist složky...');

  const rootDir = path.resolve(__dirname, '..');
  const publicDir = path.join(rootDir, 'public');
  const distDir = path.join(publicDir, 'dist');

  // Kopírování HTML souborů a dalších statických souborů
  const staticFiles = [
    'index.html',
    'chat.html',
    'profile.html',
    'callback.html',
    'login.html',
    'logout.html',
    'user-profile.html',
    'demo.html',
    'env-config.js',
    'favicon.ico',
    'manifest.json'
  ];

  staticFiles.forEach(file => {
    const sourcePath = path.join(publicDir, file);
    const destPath = path.join(distDir, file);

    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Kopírován soubor: ${file}`);
    } else {
      console.log(`Soubor ${file} neexistuje, přeskakuji`);
    }
  });

  // Seznam adresářů, které chceme zkopírovat
  const dirsToCopy = [
    'app',
    'styles',
    'images',
    'components',
    'js'
  ];

  // Ensure js directory exists in dist
  const jsDistDir = path.join(distDir, 'js');
  if (!fs.existsSync(jsDistDir)) {
    fs.mkdirSync(jsDistDir, { recursive: true });
  }

  // Kopírování adresářů
  for (const dir of dirsToCopy) {
    const srcDir = path.join(publicDir, dir);
    const destDir = path.join(distDir, dir);

    if (fs.existsSync(srcDir)) {
      console.log(`Kopíruji adresář: ${dir}`);
      copyDirRecursive(srcDir, destDir);
    } else {
      console.log(`Adresář ${dir} neexistuje, přeskakuji`);
    }
  }

  console.log('Kopírování statických souborů dokončeno');
}

// Spuštění hlavní funkce
main();
