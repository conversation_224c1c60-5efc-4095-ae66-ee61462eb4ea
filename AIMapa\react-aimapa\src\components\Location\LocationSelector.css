.location-selector {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background-color: #1e272e;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #34495e;
  color: #ecf0f1;
}

.location-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #2c3e50;
  border-bottom: 1px solid #34495e;
}

.location-selector-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #f39c12;
}

.close-button {
  background: none;
  border: none;
  color: #ecf0f1;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.close-button:hover {
  color: #e74c3c;
}

.location-selector-content {
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.search-container {
  display: flex;
  gap: 10px;
}

.search-container input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background-color: #2c3e50;
  color: #ecf0f1;
  font-size: 1rem;
}

.search-container input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.search-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  background-color: #2980b9;
}

.search-button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

.user-location-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-location-button {
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.user-location-button:hover {
  background-color: #27ae60;
}

.user-location-button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

.user-location-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50;
  border-radius: 4px;
  padding: 10px 15px;
  border: 1px solid #27ae60;
}

.selected-location-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50;
  border-radius: 4px;
  padding: 10px 15px;
  border: 1px solid #f39c12;
  margin-top: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.selected-location-container:hover {
  border-color: #e67e22;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.selected-location-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
  overflow: hidden;
}

.error-message {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #e74c3c;
}

.results-container {
  flex: 1;
  overflow-y: auto;
}

.location-results {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.location-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50;
  border-radius: 4px;
  padding: 10px 15px;
  border: 1px solid #34495e;
  transition: background-color 0.2s;
}

.location-result-item:hover {
  background-color: #34495e;
  border-color: #3498db;
}

.location-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
  overflow: hidden;
}

.location-info i {
  color: #f39c12;
  font-size: 1.2rem;
  margin-top: 3px;
}

.location-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
  overflow: hidden;
}

.location-name {
  font-weight: bold;
  color: #ecf0f1;
}

.location-address {
  color: #bdc3c7;
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.location-coordinates {
  color: #95a5a6;
  font-size: 0.8rem;
}

/* Mini mapa */
.mini-map-container {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #34495e;
  margin-top: 10px;
  position: relative;
}

.mini-map {
  width: 100%;
  height: 100%;
}

/* Elegantní ukazatel souřadnic */
.coordinates-display {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(44, 62, 80, 0.9);
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.8rem;
  color: #ecf0f1;
  border: 1px solid #3498db;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 5px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.coordinates-display:hover {
  background-color: rgba(52, 73, 94, 0.95);
  transform: translateY(-2px);
}

.coordinates-display i {
  color: #f39c12;
}

.coordinates-value {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

/* Styl pro přetažitelný marker */
.draggable-marker-hint {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(44, 62, 80, 0.9);
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.8rem;
  color: #ecf0f1;
  border: 1px solid #f39c12;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 5px;
  backdrop-filter: blur(5px);
}

.draggable-marker-hint i {
  color: #f39c12;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.select-location-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.select-location-button:hover {
  background-color: #2980b9;
}

.no-results {
  text-align: center;
  color: #95a5a6;
  padding: 20px;
  font-style: italic;
}

/* Overlay pro pozadí */
.location-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
