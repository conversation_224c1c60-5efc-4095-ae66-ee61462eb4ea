/*
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> styly pro markery na mapě
 * Verze 0.4.2
 */

:root {
  --marker-primary: #3498db;
  --marker-secondary: #9b59b6;
  --marker-success: #2ecc71;
  --marker-warning: #f39c12;
  --marker-danger: #e74c3c;
  --marker-info: #1abc9c;
  --marker-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  --marker-shadow-hover: 0 4px 15px rgba(0, 0, 0, 0.4);
  --marker-border: 2px solid rgba(255, 255, 255, 0.9);
  --marker-transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Základní marker */
.plan-marker-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--marker-primary);
  color: white;
  box-shadow: var(--marker-shadow);
  border: var(--marker-border);
  transition: var(--marker-transition);
  position: relative;
  overflow: visible;
  z-index: 500;
}

.plan-marker-inner:hover {
  transform: scale(1.1);
  box-shadow: var(--marker-shadow-hover);
}

.plan-marker-inner i {
  font-size: 18px;
  margin-bottom: 2px;
}

.plan-marker-title {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.plan-marker-inner:hover .plan-marker-title {
  opacity: 1;
  bottom: -28px;
}

/* Progress bar pro markery */
.marker-progress-container {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.marker-progress-bar {
  height: 100%;
  background-color: #2ecc71;
  transition: width 0.5s ease-out;
}

/* Odměna pro markery */
.marker-reward {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #f1c40f;
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: bounce 2s infinite;
}

/* Typy markerů podle účelu */
.plan-marker-inner.task {
  background-color: var(--marker-primary);
}

.plan-marker-inner.location {
  background-color: var(--marker-info);
}

.plan-marker-inner.warning {
  background-color: var(--marker-warning);
}

.plan-marker-inner.danger {
  background-color: var(--marker-danger);
}

.plan-marker-inner.completed {
  background-color: var(--marker-success);
}

/* Animace pro markery */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }

  50% {
    transform: scale(1.15);
    box-shadow: 0 0 0 15px rgba(52, 152, 219, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-12px) scale(1.05);
  }
  60% {
    transform: translateY(-6px) scale(1.02);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.7);
    filter: brightness(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(52, 152, 219, 0.9);
    filter: brightness(1.3);
  }
  100% {
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.7);
    filter: brightness(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes drop {
  0% {
    transform: translateY(-100px) scale(0.7);
    opacity: 0;
  }
  60% {
    transform: translateY(10px) scale(1.1);
    opacity: 1;
  }
  80% {
    transform: translateY(-5px) scale(0.95);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px) rotate(-5deg);
  }
  75% {
    transform: translateX(5px) rotate(5deg);
  }
}

@keyframes sparkle {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.5) contrast(1.2);
  }
}

/* Třídy pro animace */
.plan-marker-inner.pulse {
  animation: pulse 2s infinite;
}

.plan-marker-inner.bounce {
  animation: bounce 2s infinite;
}

.plan-marker-inner.glow {
  animation: glow 2s infinite;
}

.plan-marker-inner.spin {
  animation: spin 3s linear infinite;
}

.plan-marker-inner.drop {
  animation: drop 1s ease-out forwards;
}

.plan-marker-inner.wave {
  animation: wave 1.5s ease-in-out infinite;
}

.plan-marker-inner.sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

.plan-marker-inner.active {
  z-index: 1000;
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(52, 152, 219, 0.9);
}

/* Navigační body */
.plan-marker-inner.navigation-point {
  border: 3px solid #f39c12;
  box-shadow: 0 0 10px rgba(243, 156, 18, 0.6);
}

.plan-marker-inner.navigation-point::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px dashed #f39c12;
  border-radius: 50%;
  animation: spin 20s linear infinite;
  opacity: 0.6;
}

.plan-marker-inner.active-navigation-point {
  border: 3px solid #e74c3c;
  box-shadow: 0 0 15px rgba(231, 76, 60, 0.8);
  transform: scale(1.3);
  z-index: 1001;
}

.plan-marker-inner.active-navigation-point::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid #e74c3c;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.plan-marker-inner.active-navigation-point::after {
  content: '▶';
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: #e74c3c;
  font-size: 16px;
  animation: bounce 1.5s infinite;
}

/* Kombinované animace */
.plan-marker-inner.pulse.glow {
  animation: pulse 2s infinite, glow 2s infinite;
}

.plan-marker-inner.bounce.sparkle {
  animation: bounce 2s infinite, sparkle 1.5s infinite;
}

.plan-marker-inner.wave.glow {
  animation: wave 1.5s infinite, glow 2s infinite;
}

/* Speciální tvary markerů */
.plan-marker-inner.star {
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  width: 44px;
  height: 44px;
}

.plan-marker-inner.diamond {
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  width: 44px;
  height: 44px;
}

.plan-marker-inner.triangle {
  clip-path: polygon(50% 0%, 100% 100%, 0% 100%);
  width: 44px;
  height: 44px;
}

.plan-marker-inner.square {
  border-radius: 5px;
  width: 38px;
  height: 38px;
}

.plan-marker-inner.hexagon {
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
  width: 44px;
  height: 44px;
}

.plan-marker-inner.pin {
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  width: 38px;
  height: 38px;
}

.plan-marker-inner.pin i {
  transform: rotate(45deg);
}

/* Efekty pro markery */
.plan-marker-inner.shadow-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
  animation: shadow-pulse 2s infinite;
}

@keyframes shadow-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

.plan-marker-inner.ripple::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Popup styly */
.task-popup {
  min-width: 280px;
  max-width: 380px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  animation: popup-appear 0.3s ease-out;
}

@keyframes popup-appear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  font-size: 17px;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
  color: #2c3e50;
  position: relative;
}

.popup-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #3498db;
  animation: header-line 1s ease-in-out;
}

@keyframes header-line {
  0% {
    width: 0;
  }
  100% {
    width: 50px;
  }
}

.popup-header i {
  color: #3498db;
  font-size: 18px;
}

.popup-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popup-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #3498db;
  transition: all 0.2s;
}

.popup-section:hover {
  background-color: #f0f0f0;
  transform: translateX(3px);
}

.popup-section-title {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #34495e;
}

.popup-section-title i {
  color: #3498db;
}

.popup-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  padding: 4px 0;
}

.popup-item i {
  color: #7f8c8d;
  width: 16px;
  text-align: center;
}

.task-status {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: bold;
  margin-top: 8px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s;
}

.task-status.completed {
  background-color: rgba(46, 204, 113, 0.15);
  color: #27ae60;
  border-left: 3px solid #27ae60;
}

.task-status.completed:hover {
  background-color: rgba(46, 204, 113, 0.25);
}

.task-status.pending {
  background-color: rgba(243, 156, 18, 0.15);
  color: #d35400;
  border-left: 3px solid #f39c12;
}

.task-status.pending:hover {
  background-color: rgba(243, 156, 18, 0.25);
}

.popup-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.popup-button {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 6px;
  background-color: #3498db;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.popup-button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.popup-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.popup-button.secondary {
  background-color: #95a5a6;
}

.popup-button.secondary:hover {
  background-color: #7f8c8d;
}

.popup-button i {
  font-size: 14px;
}
