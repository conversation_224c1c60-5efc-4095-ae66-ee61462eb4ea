/* 
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> styly pro AI asistenta
 * Verze 0.4.2
 */

/* <PERSON><PERSON><PERSON><PERSON> */
.enhanced-ai-assistant {
  background-color: #1a1b26;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Hlavi<PERSON>ka chatu */
.chat-header {
  background-color: #2d3748;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h2 {
  margin: 0;
  color: #4fd1c5;
  font-size: 20px;
  font-weight: 600;
}

/* Stav API */
.api-status {
  display: flex;
  align-items: center;
}

.api-connected, .api-disconnected {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  gap: 5px;
}

.api-connected {
  background-color: rgba(72, 187, 120, 0.2);
  color: #48bb78;
}

.api-disconnected {
  background-color: rgba(245, 101, 101, 0.2);
  color: #f56565;
}

/* Oblast zpráv */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #1a1b26;
  scrollbar-width: thin;
  scrollbar-color: #4a5568 #2d3748;
}

.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #2d3748;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #4a5568;
  border-radius: 4px;
}

/* Prázdný chat */
.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #a0aec0;
  text-align: center;
  padding: 20px;
}

.empty-chat p {
  max-width: 400px;
  line-height: 1.6;
}

/* Zprávy */
.chat-message {
  max-width: 85%;
  padding: 12px 15px;
  border-radius: 10px;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-message.user {
  align-self: flex-end;
  background-color: #4fd1c5;
  color: #1a202c;
  border-bottom-right-radius: 0;
}

.chat-message.assistant {
  align-self: flex-start;
  background-color: #2d3748;
  color: #e2e8f0;
  border-bottom-left-radius: 0;
}

.chat-message.system {
  align-self: center;
  background-color: rgba(66, 153, 225, 0.2);
  color: #63b3ed;
  max-width: 90%;
  border-radius: 8px;
  font-style: italic;
}

.chat-message.error {
  align-self: center;
  background-color: rgba(245, 101, 101, 0.2);
  color: #f56565;
  max-width: 90%;
  border-radius: 8px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.message-role {
  font-weight: bold;
}

.message-time {
  opacity: 0.8;
}

.message-content {
  line-height: 1.5;
  word-break: break-word;
}

/* Lokace v zprávách */
.message-locations {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.location-chip {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background-color: rgba(66, 153, 225, 0.2);
  color: #63b3ed;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.location-chip:hover {
  background-color: rgba(66, 153, 225, 0.4);
  transform: scale(1.05);
}

.location-chip i {
  font-size: 14px;
}

/* Vstupní oblast */
.chat-input-container {
  padding: 15px;
  background-color: #2d3748;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 10px;
}

.chat-input {
  flex: 1;
  padding: 12px 15px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #1a1b26;
  color: #e2e8f0;
  resize: none;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  transition: border-color 0.3s;
  font-size: 14px;
}

.chat-input:focus {
  outline: none;
  border-color: #4fd1c5;
}

.chat-input::placeholder {
  color: #718096;
}

.chat-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4fd1c5;
  color: #1a202c;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  background-color: #38b2ac;
  transform: scale(1.05);
}

.send-button:disabled {
  background-color: #718096;
  cursor: not-allowed;
  opacity: 0.7;
}

.send-button i {
  font-size: 16px;
}

/* Ukazatel kreditů */
.credit-bar {
  padding: 10px 15px;
  background-color: #2d3748;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.credit-progress-container {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.credit-progress {
  height: 100%;
  background-color: #4fd1c5;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.credit-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #a0aec0;
}

/* Responzivní design */
@media (max-width: 768px) {
  .chat-message {
    max-width: 90%;
  }

  .chat-message.system,
  .chat-message.error {
    max-width: 95%;
  }

  .chat-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .api-status {
    width: 100%;
  }
}
