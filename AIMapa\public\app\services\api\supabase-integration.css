/**
 * Styly pro Supabase integraci
 * Verze 0.3.8.2
 */

/* Supabase konfigurace v nastavení */
#supabaseConfigSection {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

#supabaseConfigSection h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

#supabaseConfigSection .form-group {
    margin-bottom: 15px;
}

#supabaseConfigSection label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

#supabaseConfigSection .settings-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#supabaseConfigSection .api-key-input {
    display: flex;
    align-items: center;
}

#supabaseConfigSection .show-key-btn {
    margin-left: 10px;
    padding: 8px 12px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

#supabaseConfigSection .show-key-btn:hover {
    background-color: #e0e0e0;
}

#supabaseConfigSection .checkbox-container {
    margin-top: 5px;
    display: flex;
    align-items: center;
}

#supabaseConfigSection .checkbox-container input[type="checkbox"] {
    margin-right: 5px;
}

#supabaseConfigSection .connection-status {
    margin-left: 10px;
    font-size: 14px;
}

#supabaseConfigSection .connection-status.success {
    color: #2ecc71;
}

#supabaseConfigSection .connection-status.error {
    color: #e74c3c;
}

#supabaseConfigSection .connection-status.info {
    color: #3498db;
}

/* Supabase synchronizace indikátor */
.supabase-sync-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 5px;
    padding: 8px 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    display: flex;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.3s;
}

.supabase-sync-indicator.hidden {
    opacity: 0;
    pointer-events: none;
}

.supabase-sync-indicator .sync-icon {
    margin-right: 8px;
    font-size: 16px;
}

.supabase-sync-indicator .sync-text {
    font-weight: 500;
}

.supabase-sync-indicator.syncing .sync-icon {
    animation: spin 1.5s linear infinite;
}

.supabase-sync-indicator.success {
    background-color: rgba(46, 204, 113, 0.9);
    color: white;
}

.supabase-sync-indicator.error {
    background-color: rgba(231, 76, 60, 0.9);
    color: white;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tmavý režim */
body[data-theme="dark"] #supabaseConfigSection {
    border-bottom-color: #4a5568;
}

body[data-theme="dark"] #supabaseConfigSection h3 {
    color: #f7fafc;
}

body[data-theme="dark"] #supabaseConfigSection .settings-input {
    background-color: #4a5568;
    border-color: #2d3748;
    color: #f7fafc;
}

body[data-theme="dark"] #supabaseConfigSection .show-key-btn {
    background-color: #4a5568;
    border-color: #2d3748;
    color: #f7fafc;
}

body[data-theme="dark"] #supabaseConfigSection .show-key-btn:hover {
    background-color: #2d3748;
}

body[data-theme="dark"] .supabase-sync-indicator {
    background-color: rgba(45, 55, 72, 0.9);
    color: #f7fafc;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .supabase-sync-indicator.success {
    background-color: rgba(46, 204, 113, 0.9);
    color: white;
}

body[data-theme="dark"] .supabase-sync-indicator.error {
    background-color: rgba(231, 76, 60, 0.9);
    color: white;
}
