<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceBot Test - AIMapa v0.3.8.6</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
        }

        .test-section h2 {
            color: #764ba2;
            margin-top: 0;
        }

        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }

        .command-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .command-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
            font-family: monospace;
            font-size: 14px;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .browser-support {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .browser-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            font-weight: 500;
        }

        .browser-item.supported {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .browser-item.partial {
            background: #fff3e0;
            color: #ef6c00;
        }

        .browser-item.unsupported {
            background: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 VoiceBot Test - AIMapa v0.3.8.6</h1>

        <!-- Podpora prohlížeče -->
        <div class="test-section">
            <h2>🌐 Podpora prohlížeče</h2>
            <div class="browser-support">
                <div class="browser-item" id="speech-recognition">
                    <div>🎤 Rozpoznávání řeči</div>
                    <div id="recognition-status">Testování...</div>
                </div>
                <div class="browser-item" id="speech-synthesis">
                    <div>🔊 Syntéza řeči</div>
                    <div id="synthesis-status">Testování...</div>
                </div>
                <div class="browser-item" id="browser-info">
                    <div>🌍 Prohlížeč</div>
                    <div id="browser-name">Detekce...</div>
                </div>
            </div>
        </div>

        <!-- Základní testy -->
        <div class="test-section">
            <h2>🧪 Základní testy</h2>
            <button class="test-button" onclick="testSpeechSynthesis()">Test syntézy řeči</button>
            <button class="test-button secondary" onclick="testSpeechRecognition()">Test rozpoznávání řeči</button>
            <button class="test-button danger" onclick="stopAllTests()">Zastavit vše</button>
            <div id="basic-test-status"></div>
        </div>

        <!-- VoiceBot testy -->
        <div class="test-section">
            <h2>🤖 VoiceBot testy</h2>
            <button class="test-button" onclick="testVoiceBotInit()">Test inicializace VoiceBot</button>
            <button class="test-button secondary" onclick="testVoiceBotCommands()">Test hlasových příkazů</button>
            <button class="test-button secondary" onclick="testVoiceBotUI()">Test uživatelského rozhraní</button>
            <div id="voicebot-test-status"></div>
        </div>

        <!-- Příkazy k testování -->
        <div class="test-section">
            <h2>📝 Hlasové příkazy k testování</h2>
            <p>Zkuste říct následující příkazy:</p>
            <div class="command-list">
                <div class="command-item">"přiblíž"</div>
                <div class="command-item">"oddal"</div>
                <div class="command-item">"střed"</div>
                <div class="command-item">"nápověda"</div>
                <div class="command-item">"čas"</div>
                <div class="command-item">"datum"</div>
                <div class="command-item">"virtuální práce"</div>
                <div class="command-item">"moje úspěchy"</div>
            </div>
        </div>

        <!-- Log -->
        <div class="test-section">
            <h2>📋 Test log</h2>
            <div id="test-log" class="log">Čekání na testy...\n</div>
            <button class="test-button secondary" onclick="clearLog()">Vymazat log</button>
        </div>
    </div>

    <script>
        // Test log
        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = 'Log vymazán...\n';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Detekce podpory prohlížeče
        function checkBrowserSupport() {
            // Rozpoznávání řeči
            const hasRecognition = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
            const recognitionElement = document.getElementById('speech-recognition');
            const recognitionStatus = document.getElementById('recognition-status');
            
            if (hasRecognition) {
                recognitionElement.className = 'browser-item supported';
                recognitionStatus.textContent = 'Podporováno';
                log('✅ Rozpoznávání řeči je podporováno');
            } else {
                recognitionElement.className = 'browser-item unsupported';
                recognitionStatus.textContent = 'Nepodporováno';
                log('❌ Rozpoznávání řeči není podporováno');
            }

            // Syntéza řeči
            const hasSynthesis = 'speechSynthesis' in window;
            const synthesisElement = document.getElementById('speech-synthesis');
            const synthesisStatus = document.getElementById('synthesis-status');
            
            if (hasSynthesis) {
                synthesisElement.className = 'browser-item supported';
                synthesisStatus.textContent = 'Podporováno';
                log('✅ Syntéza řeči je podporována');
            } else {
                synthesisElement.className = 'browser-item unsupported';
                synthesisStatus.textContent = 'Nepodporováno';
                log('❌ Syntéza řeči není podporována');
            }

            // Informace o prohlížeči
            const browserName = getBrowserName();
            document.getElementById('browser-name').textContent = browserName;
            log(`🌍 Prohlížeč: ${browserName}`);
        }

        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            return 'Neznámý';
        }

        // Test syntézy řeči
        function testSpeechSynthesis() {
            log('🔊 Spouštím test syntézy řeči...');
            
            if (!('speechSynthesis' in window)) {
                showStatus('basic-test-status', 'Syntéza řeči není podporována', 'error');
                log('❌ Syntéza řeči není podporována');
                return;
            }

            const utterance = new SpeechSynthesisUtterance('Ahoj! Toto je test syntézy řeči v aplikaci AIMapa.');
            utterance.lang = 'cs-CZ';
            utterance.rate = 1.0;
            utterance.pitch = 1.0;
            utterance.volume = 0.8;

            utterance.onstart = () => {
                showStatus('basic-test-status', 'Syntéza řeči funguje! Poslouchejte...', 'success');
                log('✅ Syntéza řeči spuštěna');
            };

            utterance.onend = () => {
                showStatus('basic-test-status', 'Test syntézy řeči dokončen', 'info');
                log('✅ Syntéza řeči dokončena');
            };

            utterance.onerror = (event) => {
                showStatus('basic-test-status', `Chyba syntézy řeči: ${event.error}`, 'error');
                log(`❌ Chyba syntézy řeči: ${event.error}`);
            };

            speechSynthesis.speak(utterance);
        }

        // Test rozpoznávání řeči
        function testSpeechRecognition() {
            log('🎤 Spouštím test rozpoznávání řeči...');
            
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            
            if (!SpeechRecognition) {
                showStatus('basic-test-status', 'Rozpoznávání řeči není podporováno', 'error');
                log('❌ Rozpoznávání řeči není podporováno');
                return;
            }

            const recognition = new SpeechRecognition();
            recognition.lang = 'cs-CZ';
            recognition.continuous = false;
            recognition.interimResults = false;

            recognition.onstart = () => {
                showStatus('basic-test-status', 'Naslouchám... Řekněte něco!', 'info');
                log('🎤 Rozpoznávání řeči spuštěno - mluvte!');
            };

            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                showStatus('basic-test-status', `Rozpoznáno: "${transcript}"`, 'success');
                log(`✅ Rozpoznáno: "${transcript}"`);
            };

            recognition.onerror = (event) => {
                showStatus('basic-test-status', `Chyba rozpoznávání: ${event.error}`, 'error');
                log(`❌ Chyba rozpoznávání: ${event.error}`);
            };

            recognition.onend = () => {
                log('🎤 Rozpoznávání řeči ukončeno');
            };

            recognition.start();
        }

        // Zastavení všech testů
        function stopAllTests() {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();
            }
            showStatus('basic-test-status', 'Všechny testy zastaveny', 'info');
            log('⏹️ Všechny testy zastaveny');
        }

        // Test VoiceBot inicializace
        function testVoiceBotInit() {
            log('🤖 Testování inicializace VoiceBot...');
            
            if (typeof window.VoiceBot === 'undefined') {
                showStatus('voicebot-test-status', 'VoiceBot není načten', 'error');
                log('❌ VoiceBot objekt není dostupný');
                return;
            }

            if (window.VoiceBot.isInitialized) {
                showStatus('voicebot-test-status', 'VoiceBot je úspěšně inicializován', 'success');
                log('✅ VoiceBot je inicializován');
            } else {
                showStatus('voicebot-test-status', 'VoiceBot není inicializován', 'error');
                log('❌ VoiceBot není inicializován');
            }
        }

        // Test VoiceBot příkazů
        function testVoiceBotCommands() {
            log('🗣️ Testování VoiceBot příkazů...');
            
            if (typeof window.VoiceBot === 'undefined' || !window.VoiceBot.isInitialized) {
                showStatus('voicebot-test-status', 'VoiceBot není dostupný', 'error');
                log('❌ VoiceBot není dostupný pro testování příkazů');
                return;
            }

            const testCommands = ['čas', 'datum', 'nápověda'];
            let commandIndex = 0;

            function testNextCommand() {
                if (commandIndex < testCommands.length) {
                    const command = testCommands[commandIndex];
                    log(`🧪 Testování příkazu: "${command}"`);
                    
                    // Simulace hlasového příkazu
                    window.VoiceBot.processVoiceCommand(command);
                    
                    commandIndex++;
                    setTimeout(testNextCommand, 2000);
                } else {
                    showStatus('voicebot-test-status', 'Test příkazů dokončen', 'success');
                    log('✅ Test všech příkazů dokončen');
                }
            }

            showStatus('voicebot-test-status', 'Testování příkazů...', 'info');
            testNextCommand();
        }

        // Test VoiceBot UI
        function testVoiceBotUI() {
            log('🎨 Testování VoiceBot UI...');
            
            const voiceBotContainer = document.getElementById('voicebot-container');
            
            if (voiceBotContainer) {
                showStatus('voicebot-test-status', 'VoiceBot UI je přítomno', 'success');
                log('✅ VoiceBot UI kontejner nalezen');
                
                // Test viditelnosti
                const isVisible = voiceBotContainer.style.display !== 'none';
                log(`👁️ VoiceBot UI je ${isVisible ? 'viditelné' : 'skryté'}`);
                
                // Test tlačítek
                const buttons = voiceBotContainer.querySelectorAll('button');
                log(`🔘 Nalezeno ${buttons.length} tlačítek v UI`);
                
            } else {
                showStatus('voicebot-test-status', 'VoiceBot UI není přítomno', 'error');
                log('❌ VoiceBot UI kontejner nenalezen');
            }
        }

        // Inicializace při načtení stránky
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Spouštím testy VoiceBot...');
            checkBrowserSupport();
            
            // Čekání na načtení VoiceBot
            setTimeout(() => {
                testVoiceBotInit();
            }, 2000);
        });
    </script>
</body>
</html>
