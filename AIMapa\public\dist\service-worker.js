if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()})).then((()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e})));self.define=(i,c)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(s[r])return;let l={};const t=e=>n(e,r),o={module:{uri:r},exports:l,require:t};s[r]=Promise.all(i.map((e=>o[e]||t(e)))).then((e=>(c(...e),l)))}}define(["./workbox-3175ccc6"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"chat.html",revision:"1cbe8bba736dd771d8224f7168c1965f"},{url:"index.html",revision:"60b82a7b51a3bc4bdc02d58dc09396d3"},{url:"js/auth.31d6cfe0d16ae931b73c.min.js",revision:null},{url:"js/auth0-bundle.43745bf1af764cfffebb.min.js",revision:null},{url:"js/chat.31d6cfe0d16ae931b73c.min.js",revision:null},{url:"js/main.b21208263df11332c6a7.min.js",revision:null},{url:"js/supabase.31d6cfe0d16ae931b73c.min.js",revision:null},{url:"js/sync.31d6cfe0d16ae931b73c.min.js",revision:null}],{}),e.registerRoute(/\.(?:png|jpg|jpeg|svg|gif)$/,new e.CacheFirst({cacheName:"images",plugins:[new e.ExpirationPlugin({maxEntries:60,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\.(?:js|css)$/,new e.StaleWhileRevalidate({cacheName:"static-resources",plugins:[]}),"GET"),e.registerRoute(/^https:\/\/api\.mapbox\.com\//,new e.CacheFirst({cacheName:"mapbox-api",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:604800})]}),"GET")}));
//# sourceMappingURL=service-worker.js.map
