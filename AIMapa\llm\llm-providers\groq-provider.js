/**
 * Groq Provider
 * Verze 0.4.4
 *
 * Provider pro Groq API - rychlá inference
 */

/**
 * Třída pro komunikaci s Groq API
 */
class GroqProvider {
  /**
   * Konstruktor
   * @param {Object} options - Konfigurační objekt
   * @param {string} options.apiKey - Groq API klíč
   * @param {string} options.model - Název modelu
   * @param {number} options.temperature - Teplota (0.0 - 1.0)
   * @param {number} options.maxTokens - Maximální počet tokenů
   */
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.model = options.model || 'llama3-8b-8192';
    this.temperature = options.temperature || 0.7;
    this.maxTokens = options.maxTokens || 1000;
    this.baseUrl = 'https://api.groq.com/openai/v1';

    if (!this.apiKey) {
      throw new Error('Groq API klíč je povinný');
    }

    console.log(`Groq Provider inicializován s modelem ${this.model}`);
  }

  /**
   * Získání odpovědi od Groq
   * @param {string} prompt - Prompt pro model
   * @returns {Promise<Object>} Odpověď od modelu
   */
  async getCompletion(prompt) {
    try {
      const url = `${this.baseUrl}/chat/completions`;
      
      const requestBody = {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        top_p: 1,
        stream: false
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Groq API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('Groq nevrátil žádnou odpověď');
      }

      return {
        text: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        model: this.model,
        provider: 'groq'
      };
    } catch (error) {
      console.error('Chyba při komunikaci s Groq:', error);
      throw error;
    }
  }

  /**
   * Získání seznamu dostupných modelů
   * @returns {Promise<Array>} Seznam dostupných modelů
   */
  async getAvailableModels() {
    try {
      const url = `${this.baseUrl}/models`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Groq API chyba: ${response.status} - ${errorData}`);
      }

      const data = await response.json();
      
      return data.data?.map(model => ({
        id: model.id,
        object: model.object,
        created: model.created,
        owned_by: model.owned_by
      })) || [];
    } catch (error) {
      console.error('Chyba při získávání seznamu modelů z Groq:', error);
      throw error;
    }
  }

  /**
   * Test připojení k Groq
   * @returns {Promise<boolean>} True pokud je připojení úspěšné
   */
  async testConnection() {
    try {
      await this.getCompletion('Test připojení');
      return true;
    } catch (error) {
      console.error('Test připojení k Groq selhal:', error);
      return false;
    }
  }

  /**
   * Získání informací o modelu
   * @returns {Object} Informace o modelu
   */
  getModelInfo() {
    return {
      provider: 'groq',
      model: this.model,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
      baseUrl: this.baseUrl
    };
  }

  /**
   * Získání podporovaných modelů pro Groq
   * @returns {Array} Seznam podporovaných modelů
   */
  static getSupportedModels() {
    return [
      'llama3-8b-8192',
      'llama3-70b-8192',
      'mixtral-8x7b-32768',
      'gemma-7b-it',
      'gemma2-9b-it'
    ];
  }
}

module.exports = GroqProvider;
