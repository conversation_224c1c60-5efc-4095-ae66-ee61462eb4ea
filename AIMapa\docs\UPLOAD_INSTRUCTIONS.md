# Instrukce pro nahrání verze *******.8 na GitHub

Tato verze obsahuje následující nové soubory a změny:

## Nové soubory:
- `food-services.js` - Modul pro služby jídla a pití
- `medical-services.js` - Modul pro lékařské služby
- `transport-services.js` - Modul pro veřejnou dopravu

## Změněné soubory:
- `index.html` - Přidány odkazy na nové JavaScript soubory
- `commands-menu.js` - Aktualizováno pro podporu nových modulů
- `script.js` - Aktualizována verze
- `feedback-survey.js` - Aktualizována verze
- `CHANGELOG.md` - Přidány informace o nové verzi

## Postup pro nahrání na GitHub:

1. Otevřete GitHub Desktop nebo jiného Git klienta
2. Vytvořte novou větev `v*******.8` z větve `v*******.7`
3. Commitněte všechny změny s popisem "Version *******.8 - Funkční panel možností vedle chatu"
4. Pushněte změny na GitHub
5. Vytvořte tag `v*******.8` pro tento commit
6. Pushněte tag na GitHub

Alternativně můžete použít následující Git příkazy:

```bash
# Přepnutí na větev v*******.7
git checkout v*******.7

# Vytvoření nové větve v*******.8
git checkout -b v*******.8

# Přidání všech změn
git add .

# Vytvoření commitu
git commit -m "Version *******.8 - Funkční panel možností vedle chatu"

# Push na GitHub
git push -u origin v*******.8

# Vytvoření tagu
git tag v*******.8

# Push tagu na GitHub
git push origin v*******.8
```

## Shrnutí změn v verzi *******.8:

### Nové funkce:
- Přidány funkční moduly pro služby jídla a pití (jídlo, pizza, energy drinky, krkovička)
- Přidány funkční moduly pro lékařské služby (lékař, zubař, lékárna)
- Přidán funkční modul pro veřejnou dopravu s vyhledáváním spojení
- Implementováno zobrazení prodejních oken s možností objednávky
- Přidána možnost objednání k lékaři a zubaři
- Přidána možnost nákupu jízdenek na veřejnou dopravu

### Vylepšení:
- Přidána funkčnost všem tlačítkům v panelu možností
- Vylepšena interakce s uživatelem při použití příkazů
- Optimalizováno zobrazení všech nových oken a dialogů
- Přidáno získávání XP za použití různých služeb
