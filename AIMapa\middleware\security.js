/**
 * Middleware pro zabezpečení API
 * Verze *******
 */

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { AppError } = require('./errorHandler');

// Rate limiting
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minut
    max: 100, // limit 100 požadavků na IP
    message: {
        error: '<PERSON><PERSON><PERSON><PERSON>š mnoho požadavků z této IP adresy, zkuste to prosím později',
        retryAfter: '15 minut'
    }
});

// Přísnější limit pro autentizační endpointy
const authLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hodina
    max: 5, // limit 5 pokusů na IP
    message: {
        error: '<PERSON><PERSON><PERSON><PERSON><PERSON> mnoho pokusů o přihlášení, zkuste to prosím později',
        retryAfter: '60 minut'
    }
});

// Kontrola oprávnění
const checkPermissions = (requiredPermissions) => {
    return (req, res, next) => {
        try {
            if (!req.oidc.isAuthenticated()) {
                throw new AppError(401, 'Nepřihlášený uživatel');
            }

            const userPermissions = req.oidc.user.permissions || [];
            const hasAllPermissions = requiredPermissions.every(
                permission => userPermissions.includes(permission)
            );

            if (!hasAllPermissions) {
                throw new AppError(403, 'Nedostatečná oprávnění');
            }

            next();
        } catch (error) {
            next(error);
        }
    };
};

// Konfigurace Helmet pro zabezpečení hlaviček
const helmetConfig = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://cdn.auth0.com'],
            styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
            imgSrc: ["'self'", 'data:', 'https:'],
            connectSrc: ["'self'", 'https://*.auth0.com', 'https://*.supabase.co'],
            fontSrc: ["'self'", 'https://fonts.gstatic.com'],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'self'", 'https://*.auth0.com']
        }
    },
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" }
});

module.exports = {
    apiLimiter,
    authLimiter,
    checkPermissions,
    helmetConfig
};