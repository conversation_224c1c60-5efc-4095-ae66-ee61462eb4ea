# Auth0 konfigurace
AUTH0_DOMAIN=your-tenant.auth0.com
AUTH0_CLIENT_ID=your-client-id
AUTH0_CLIENT_SECRET=your-client-secret
AUTH0_CALLBACK_URL=http://localhost:3000/callback
AUTH0_LOGOUT_URL=http://localhost:3000
AUTH0_SCOPE="openid profile email read:users read:user_idp_tokens"
AUTH0_AUDIENCE=https://your-tenant.auth0.com/api/v2/

# Supabase konfigurace
SUPABASE_URL=your-project-url.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# Aplikační konfigurace
NODE_ENV=development
PORT=3000
BASE_URL=http://localhost:3000

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minut
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000  # 1 hodina
AUTH_RATE_LIMIT_MAX_ATTEMPTS=5

# Stripe (volitelné)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# LLM API konfigurace
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Anthropic
ANTHROPIC_API_KEY=your-anthropic-api-key

# Cohere
COHERE_API_KEY=your-cohere-api-key

# Gemini
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-1.5-flash
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=1000

# Obecné nastavení LLM
LLM_PROVIDER=openai
LLM_MODEL=gpt-4
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=1000
LLM_CACHE_ENABLED=true
LLM_CACHE_EXPIRATION=3600
LLM_LOG_LEVEL=info
LLM_LOG_PROMPTS=false
LLM_LOG_RESPONSES=false