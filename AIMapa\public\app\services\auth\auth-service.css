/**
 * Styly pro jednotný autentizační modul
 * Verze 0.3.8.7
 */

/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> */
.auth-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background-color: var(--primary-color, #4a90e2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.auth-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.auth-button i {
    font-size: 18px;
}

/* Stavy přihlášení */
.auth-button.logged-in {
    background-color: #4caf50;
}

.auth-button.logged-out {
    background-color: #f44336;
}

/* Uživatelské menu */
.user-menu {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1000;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 250px;
    overflow: hidden;
    display: none;
}

.user-menu.visible {
    display: block;
    animation: slideDown 0.3s ease;
}

.user-menu-header {
    padding: 15px;
    background-color: var(--primary-color, #4a90e2);
    color: white;
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
    background-color: #e0e0e0;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: bold;
    margin: 0;
    font-size: 16px;
}

.user-email {
    margin: 0;
    font-size: 12px;
    opacity: 0.8;
}

.user-menu-items {
    padding: 10px 0;
}

.user-menu-item {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.user-menu-item:hover {
    background-color: #f5f5f5;
}

.user-menu-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: var(--primary-color, #4a90e2);
}

.user-menu-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 5px 0;
}

/* Přihlašovací modální okno */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.auth-modal.visible {
    opacity: 1;
    visibility: visible;
}

.auth-modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 400px;
    max-width: 90%;
    overflow: hidden;
    animation: zoomIn 0.3s ease;
}

.auth-modal-header {
    padding: 20px;
    background-color: var(--primary-color, #4a90e2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.auth-modal-header h2 {
    margin: 0;
    font-size: 20px;
}

.auth-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.auth-modal-body {
    padding: 20px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.auth-tab {
    padding: 10px 20px;
    cursor: pointer;
    flex: 1;
    text-align: center;
    transition: all 0.2s ease;
}

.auth-tab.active {
    border-bottom: 2px solid var(--primary-color, #4a90e2);
    color: var(--primary-color, #4a90e2);
    font-weight: bold;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form-group {
    margin-bottom: 15px;
}

.auth-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 14px;
}

.auth-form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.auth-form-group input:focus {
    border-color: var(--primary-color, #4a90e2);
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.auth-form-actions {
    margin-top: 20px;
}

.auth-form-actions button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color, #4a90e2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.auth-form-actions button:hover {
    background-color: var(--primary-color-dark, #3a80d2);
}

.auth-form-actions button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.auth-form-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
}

.auth-form-footer a {
    color: var(--primary-color, #4a90e2);
    text-decoration: none;
}

.auth-form-footer a:hover {
    text-decoration: underline;
}

.auth-providers {
    margin-top: 20px;
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

.auth-providers-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.auth-provider-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.auth-provider-button {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.auth-provider-button:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

.auth-provider-button img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

/* Animace */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responzivní design */
@media (max-width: 768px) {
    .auth-button {
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
    }

    .user-menu {
        top: 56px;
        right: 10px;
        width: 220px;
    }

    .auth-modal-content {
        width: 320px;
    }
}
